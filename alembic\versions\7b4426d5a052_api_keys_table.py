"""Api keys table

Revision ID: 7b4426d5a052
Revises: 07edef66bb67
Create Date: 2025-05-05 12:10:13.611597

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7b4426d5a052'
down_revision: Union[str, None] = '07edef66bb67'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_keys',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key_name', sa.Enum('STRIPE_KEY', 'GOOGLE_OAUTH_KEY', 'GITHUB_OAUTH_KEY', 'MICROSOFT_OAUTH_KEY', 'OPENAI_KEY', name='apikeynameenum'), nullable=False),
    sa.Column('key_id', postgresql.JSON(astext_type=sa.Text()), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_api_keys_id'), 'api_keys', ['id'], unique=False)
    op.create_index(op.f('ix_api_keys_key_name'), 'api_keys', ['key_name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_api_keys_key_name'), table_name='api_keys')
    op.drop_index(op.f('ix_api_keys_id'), table_name='api_keys')
    op.drop_table('api_keys')
    # ### end Alembic commands ###
