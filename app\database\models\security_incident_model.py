from sqlalchemy import Column, Integer, String, DateTime
from datetime import datetime
from app.database.base import Base

class SecurityIncident(Base):
    __tablename__ = "security_incidents"

    id = Column(Integer, primary_key=True, index=True)
    admin_id = Column(Integer)
    reason = Column(String)
    action = Column(String)
    ip_address = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
