from fastapi import APIRouter, Depends, Query
from datetime import date, datetime
from sqlalchemy.orm import Session
from app.database.session import get_db
from app.database.dependencies import get_current_user, role_required
from app.services.dashboard_service import DashboardService

router = APIRouter()


@router.get("/get_active_users")
def get_active_users(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
                    start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                    end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    return DashboardService.get_active_users_service(db,start_date,end_date)

@router.get("/get_total_revenue")
def get_total_revenue(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
                      start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                    end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    return DashboardService.get_total_revenue_service(db,start_date,end_date)


@router.get("/get_active_subscriptions")
def get_active_subscriptions(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
                             start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                             end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    return DashboardService.get_active_subscriptions_service(db,start_date,end_date)

@router.get("/get_total_prd_generated")
def get_total_prd_generated(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
                             start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                             end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    return DashboardService.get_total_prd_generated(db,start_date,end_date)

@router.get("/get_user_month_wise_data")
def get_user_month_wise_data(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db)
                             ,start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                             end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    return DashboardService.get_user_month_wise_data(db,start_date,end_date)

@router.get("/get_payment_month_wise_data")
def get_payment_month_wise_data(
    current_user=Depends(role_required(["Admin"])),
    db: Session = Depends(get_db),
    start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
    end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD")
):
    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
    
    return DashboardService.get_payment_month_wise_data(db, start_datetime, end_datetime)

@router.get("/get_active_or_inactive_subscrptions")
def get_active_or_inactive_subscrptions(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
    start_date:date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
    end_date:date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
    return DashboardService.get_active_or_inactive_subscrptions(db ,start_datetime,end_datetime)


@router.get("/get_prd_generated_month_wise_data")
def get_prd_generated_month_wise_data(current_user=Depends(role_required(["Admin"])),db: Session = Depends(get_db),
    start_date:date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
    end_date:date = Query(None, description="Filter users created before this date YYYY-MM-DD")):
    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
    return DashboardService.get_prd_generated_month_wise_data(db ,start_datetime,end_datetime)
