"""GuestUsage 

Revision ID: cca54e5f2664
Revises: 0c472ecc4e6f
Create Date: 2025-06-05 10:36:50.941612

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cca54e5f2664'
down_revision: Union[str, None] = '0c472ecc4e6f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('guest_usage',
    sa.Column('ip_address', sa.String(), nullable=False),
    sa.Column('upload_count', sa.Integer(), nullable=True),
    sa.Column('last_accessed', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('ip_address')
    )
    op.create_index(op.f('ix_guest_usage_ip_address'), 'guest_usage', ['ip_address'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_guest_usage_ip_address'), table_name='guest_usage')
    op.drop_table('guest_usage')
    # ### end Alembic commands ###
