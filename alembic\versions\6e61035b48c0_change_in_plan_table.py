"""Change in plan table

Revision ID: 6e61035b48c0
Revises: 58f55cb43124
Create Date: 2025-04-29 11:30:28.343206

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6e61035b48c0'
down_revision: Union[str, None] = '58f55cb43124'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE plans ALTER COLUMN duration TYPE INTEGER USING duration::integer')

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('plans', 'duration',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    # ### end Alembic commands ###
