"""Free Uploads Table Added

Revision ID: a723bc584623
Revises: 035d121cb364
Create Date: 2025-04-28 10:36:49.744570

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a723bc584623'
down_revision: Union[str, None] = '035d121cb364'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('free_uploads',
    sa.Column('free_upload_id', sa.Integer(), nullable=False),
    sa.Column('uploads', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('free_upload_id')
    )
    op.create_index(op.f('ix_free_uploads_free_upload_id'), 'free_uploads', ['free_upload_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_free_uploads_free_upload_id'), table_name='free_uploads')
    op.drop_table('free_uploads')
    # ### end Alembic commands ###
