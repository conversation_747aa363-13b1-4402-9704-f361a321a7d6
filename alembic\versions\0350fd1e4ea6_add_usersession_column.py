"""add usersession column

Revision ID: 0350fd1e4ea6
Revises: 9df93e426312
Create Date: 2025-07-02 12:52:11.741866

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0350fd1e4ea6'
down_revision: Union[str, None] = '9df93e426312'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_sessions', sa.Column('session_id', sa.String(), nullable=False))
    op.add_column('user_sessions', sa.Column('jti_id', sa.String(), nullable=True))
    op.drop_index('ix_user_sessions_sessjtiion_id', table_name='user_sessions')
    op.create_index(op.f('ix_user_sessions_session_id'), 'user_sessions', ['session_id'], unique=False)
    op.create_unique_constraint(None, 'user_sessions', ['jti_id'])
    op.drop_column('user_sessions', 'sessjtiion_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_sessions', sa.Column('sessjtiion_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'user_sessions', type_='unique')
    op.drop_index(op.f('ix_user_sessions_session_id'), table_name='user_sessions')
    op.create_index('ix_user_sessions_sessjtiion_id', 'user_sessions', ['sessjtiion_id'], unique=False)
    op.drop_column('user_sessions', 'jti_id')
    op.drop_column('user_sessions', 'session_id')
    # ### end Alembic commands ###
