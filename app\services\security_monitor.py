from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.database.models.security_incident_model import SecurityIncident
from app.database.models.audit_logs_model import AuditLog

class SecurityMonitor:

    @staticmethod
    def check_for_breach(db: Session, admin_id: int, ip: str, action: str):
        now = datetime.now()

        # 1. Too many deletions in 5 minutes
        if "DELETE" in action:
            five_minutes_ago = now - timedelta(minutes=5)
            count = db.query(AuditLog).filter(
                AuditLog.admin_id == admin_id,
                AuditLog.action.like("DELETE%"),
                AuditLog.timestamp >= five_minutes_ago
            ).count()

            if count > 5:
                SecurityMonitor.log_incident(
                    db, admin_id, ip, action, "Excessive DELETEs in 5 min"
                )

        # 2. Multiple IP addresses in 24h
        twenty_four_hrs_ago = now - timedelta(hours=24)
        ip_count = db.query(AuditLog.ip_address).filter(
            AuditLog.admin_id == admin_id,
            AuditLog.timestamp >= twenty_four_hrs_ago
        ).distinct().count()

        if ip_count > 3:
            SecurityMonitor.log_incident(
                db, admin_id, ip, action, "Multiple IPs for same admin"
            )

    @staticmethod
    def log_incident(db: Session, admin_id: int, ip: str, action: str, reason: str):
        db.add(SecurityIncident(
            admin_id=admin_id,
            action=action,
            ip_address=ip,
            reason=reason,
        ))
        db.commit()
