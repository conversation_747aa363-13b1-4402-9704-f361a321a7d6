"""fixes

Revision ID: a0029b44419c
Revises: 0350fd1e4ea6
Create Date: 2025-07-02 12:58:29.049329

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0029b44419c'
down_revision: Union[str, None] = '0350fd1e4ea6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_sessions', sa.Column('id', sa.Integer(), nullable=False))
    op.drop_index('ix_user_sessions_session_id', table_name='user_sessions')
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.drop_column('user_sessions', 'session_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_sessions', sa.Column('session_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.create_index('ix_user_sessions_session_id', 'user_sessions', ['session_id'], unique=False)
    op.drop_column('user_sessions', 'id')
    # ### end Alembic commands ###
