from fastapi import APIRouter,Depends,Query
from app.services.content_page_service import ContentPageService
from app.schemas.content_page_schema import ContentPageCreate,ContentPageUpdate
from sqlalchemy.orm import Session
from app.database.session import get_db
from app.database.dependencies import role_required

router = APIRouter()

@router.post("/create_content_pages")
async def create_content_page(content_page_data: ContentPageCreate,db:Session = Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return  ContentPageService.create_content_page_service(content_page_data,db)


@router.get("/get_all_content_pages")
async def get_content_pages(db: Session = Depends(get_db),page: int = Query(1, ge=1),page_size: int = Query(10, ge=1),sort_by: str = Query("id"),sort_order: str = Query("asc"),              
    search: str = Query(None),current_user=Depends(role_required(["Admin"]))):
    return ContentPageService.get_content_pages_service(
        db=db,
        page=page,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=sort_order,
        search=search
    )

@router.get("/get_content_page")
async def get_content_page(slug: str,db: Session = Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return ContentPageService.get_content_page(slug,db)

@router.delete("/delete_content_page")
async def delete_content_page(id:int, db:Session =Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return ContentPageService.delete_content_page_service(id,db)

@router.put("/update_content_page")
async def update_content_page(content_page_data: ContentPageUpdate,db:Session = Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return ContentPageService.update_content_page_service(content_page_data,db)