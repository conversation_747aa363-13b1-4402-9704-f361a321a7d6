from sqlalchemy import Column, Integer, String, DateTime, Boolean
from app.database.base import Base
from sqlalchemy.orm import relationship


class ContentPage(Base):
    __tablename__ = "content_pages"
    id = Column(Integer, primary_key=True, index=True)
    slug = Column(String, nullable=False)
    title = Column(String, nullable=False)
    content = Column(String, nullable=False)
    is_active = Column(Boolean, nullable=False)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)