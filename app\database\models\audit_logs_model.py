from sqlalchemy import Column, Integer, String, DateTime
from datetime import datetime
from app.database.base import Base 

class AuditLog(Base):
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    admin_id = Column(Integer, nullable=True)
    action = Column(String)
    ip_address = Column(String)
    status_code = Column(Integer,nullable=True)
    user_agent = Column(String,nullable=True)
    response = Column(String,nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
