from fastapi import APIRouter, Depends,Request
from app.database.session import get_db
from sqlalchemy.orm import Session
from app.schemas.stripe_payment_schema import PaymentCreate
from app.services.stripe_payment_service import StripePaymentService
from app.database.dependencies import get_current_user

router = APIRouter()

@router.post("/create_payment_checkout_session")
def create_payment_checkout_session(payment_data: PaymentCreate,current_user=Depends(get_current_user), db: Session = Depends(get_db)):
    return StripePaymentService.create_payment_checkout_session(payment_data,current_user,db)

@router.post("/stripe_webhook")
async def stripe_webhook(request:Request,db:Session = Depends(get_db)):
    return await StripePaymentService.stripe_webhook_service(request,db)

@router.post("/cancel_subscription_autopayment")
def cancel_subscription(current_user=Depends(get_current_user),db:Session = Depends(get_db)):
    return StripePaymentService.cancel_subscription_autopayment_service(current_user,db)

@router.post("/get_payment_details")
def get_payment_details(session_id:str,current_user=Depends(get_current_user),db:Session = Depends(get_db)):
    return StripePaymentService.get_payment_details_service(session_id,current_user,db)