from fastapi import HTTPException,status
from app.database.models.content_page_model import ContentPage
from datetime import datetime
from sqlalchemy import or_, asc, desc




class ContentPageService:


    @staticmethod
    def create_content_page_service(content_page_data,db):
        chk_content = db.query(ContentPage).filter(ContentPage.slug == content_page_data.slug).first()
        if chk_content:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Slug already exists")
        if content_page_data.title == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title cannot be empty")
        if content_page_data.content == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Content cannot be empty")
        if content_page_data.slug == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Slug cannot be empty")
        if content_page_data.is_active == None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="is_active cannot be empty")
        content_page = ContentPage(
            title=content_page_data.title,
            content=content_page_data.content,
            slug=content_page_data.slug,
            is_active=content_page_data.is_active,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(content_page)
        db.commit()
        db.refresh(content_page)
        return {"message":"Content page created successfully!"}
    
    @staticmethod
    def get_content_pages_service(db, page: int, page_size: int, sort_by: str, sort_order: str, search: str):
        query = db.query(ContentPage)

        if search:
            query = query.filter(
                or_(
                    ContentPage.title.ilike(f"%{search}%"),
                    ContentPage.description.ilike(f"%{search}%")
                )
            )

        sort_column = getattr(ContentPage, sort_by, None)
        if sort_column is not None:
            query = query.order_by(asc(sort_column) if sort_order == "asc" else desc(sort_column))

        total = query.count()
        items = query.offset((page - 1) * page_size).limit(page_size).all()

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": items
        }
    
    @staticmethod
    def get_content_page(slug,db):
        content_page = db.query(ContentPage).filter(ContentPage.slug == slug).first()
        if not content_page:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Content page not found")
        return content_page
    
    @staticmethod
    def delete_content_page_service(id,db):
        content_page = db.query(ContentPage).filter(ContentPage.id == id).first()
        if not content_page:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Content page not found")
        db.delete(content_page)
        db.commit()
        return {"message":"Content page deleted successfully!"}
    
    @staticmethod 
    def update_content_page_service(content_page_data,db):
        content_page = db.query(ContentPage).filter(ContentPage.id == content_page_data.id).first()
        if not content_page:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Content page not found")
        if content_page_data.title == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title cannot be empty")
        if content_page_data.content == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Content cannot be empty")
        if content_page_data.slug == "":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Slug cannot be empty")
        if content_page_data.is_active == None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="is_active cannot be empty")
        content_page.title = content_page_data.title
        content_page.content = content_page_data.content
        content_page.slug = content_page.slug
        content_page.is_active = content_page_data.is_active
        content_page.updated_at = datetime.now()
        db.commit()
        db.refresh(content_page)
        return {"message":"Content page updated successfully!"}
