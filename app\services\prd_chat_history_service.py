from fastapi import HTT<PERSON>Exception, status
from app.database.models.chat_history_model import Chat<PERSON><PERSON>ory
from app.database.models.chat_session_model import ChatSession
import json
from app.core.redis import redis_client
import re
from app.database.enum import RoleEnum

class PRDChatHistoryService:

    @staticmethod
    def get_chat_history(user_id : int, db, page: int = 1, page_size: int = 10):
        cache_key = f"chat_history_user_{user_id}_page_{page}_size_{page_size}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)

        offset = (page - 1) * page_size
        total_sessions = db.query(ChatSession).filter(
            ChatSession.user_id == user_id
        ).count()

        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == user_id
        ).order_by(ChatSession.created_at.desc()) \
        .offset(offset).limit(page_size).all()

        if not sessions:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat history found for this page."
            )

        history = []

        for session in sessions:
            user_messages = db.query(ChatHistory).filter(
                ChatHistory.session_id == session.session_id,
                ChatHistory.role == "human"
            ).order_by(ChatHistory.created_at.asc()).limit(3).all()

            filenames = []
            for msg in user_messages:                
                image_matches = re.findall(r"!\[([^\]]+)\]\((?:/static/prd_image_uploads/.*?)\)", msg.message)
                filenames.extend(name.strip() for name in image_matches)

                lines = msg.message.splitlines()
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith("Uploaded files:") or re.match(r"!\[.*?\]\(.*?\)", line):
                        continue
                    filenames.append(line)

            history.append({
                "session_id": session.session_id,
                "created_at": session.created_at.isoformat(),
                "messages": filenames
            })

        result = {
            "page": page,
            "page_size": page_size,
            "sessions": history
        }

        redis_client.setex(cache_key, 60 * 5, json.dumps(result))
        return result



    @staticmethod
    def get_chat_data_by_chat_id(current_user, db, session_id):
        cache_key = f"chat_data_user_{current_user.id}_chat_{session_id}"
        cached = redis_client.get(cache_key)
        if cached:
            return json.loads(cached)

        query = db.query(ChatHistory).join(
            ChatSession, ChatSession.session_id == ChatHistory.session_id
        ).filter(ChatHistory.session_id == session_id)

        # Only apply user filter if the user is not an admin
        if current_user.role != RoleEnum.Admin:
            query = query.filter(ChatSession.user_id == current_user.id)

        chat_data = query.order_by(ChatHistory.created_at.asc()).all()

        if not chat_data:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat data found for this chat id."
            )
        serialized = []

        for row in chat_data:
            base_item = {
                "chat_id": row.chat_id,
                "session_id": row.session_id,
                "created_at": row.created_at.isoformat(),
                "role": row.role,
            }
            if row.role == "ai":
                base_item["message"] = row.message

            elif row.role == "human":
                filenames = []
                image_urls = []

                image_matches = re.findall(
                    r"!\[([^\]]+)\]\((/static/prd_image_uploads/[^\)]+)\)", row.message)
                for name, url in image_matches:
                    filenames.append(name.strip())
                    image_urls.append(url.strip())

                lines = row.message.splitlines()
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith("Uploaded files:") or re.match(r"!\[.*?\]\(.*?\)", line):
                        continue
                    filenames.append(line)

                base_item["message"] = filenames
                base_item["image_urls"] = image_urls

            serialized.append(base_item)
        redis_client.setex(cache_key, 60 * 5, json.dumps(serialized))
        return serialized




    @staticmethod
    def get_guest_user_chat_history(db, page: int = 1, page_size: int = 10):
        offset = (page - 1) * page_size
        total_sessions = db.query(ChatSession).filter(
            ChatSession.user_id == None
        ).count()
        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == None
        ).order_by(ChatSession.created_at.desc()) \
        .offset(offset).limit(page_size).all()

        if not sessions:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat history found for this page."
            )
        history = []
        for session in sessions:
            user_messages = db.query(ChatHistory).filter(
                ChatHistory.session_id == session.session_id,
                ChatHistory.role == "human"
            ).order_by(ChatHistory.created_at.asc()).limit(3).all()

            messages = []
            for msg in user_messages:
                # Capture image filenames from markdown
                image_matches = re.findall(r"!\[([^\]]+)\]\((?:/static/prd_image_uploads/.*?)\)", msg.message)
                for name in image_matches:
                    messages.append(name.strip())

                # Split and add each line of message excluding only image markdowns
                lines = msg.message.splitlines()
                for line in lines:
                    line = line.strip()
                    # Skip only pure image markdown lines
                    if re.match(r"^!\[.*?\]\(.*?\)$", line):
                        continue
                    # Include user-entered text
                    messages.append(line)

            # Filter out duplicates and empty lines
            clean_messages = [m for m in messages if m]
            history.append({
                "session_id": session.session_id,
                "created_at": session.created_at.isoformat(),
                "messages": clean_messages
            })
        result = {
            "page": page,
            "page_size": page_size,
            "sessions": history
        }
        return result
    




