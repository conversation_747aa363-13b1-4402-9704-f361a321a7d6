from datetime import datetime, timedelta
from jose import JWTError
import uuid
from app.core.config import settings
from sqlalchemy.orm import Session
from app.database.models.user_model import RevokedToken
from fastapi import HTTPException
from typing import Optional
import jwt
from fastapi import HTTPException, status
from jwt import ExpiredSignatureError, InvalidTokenError
import hmac
import hashlib
import base64
import urllib.parse

def create_access_token(data: dict, expire_minutes: Optional[int] = settings.ACCESS_TOKEN_EXPIRE_MINUTES):
    to_encode = data.copy()
    expire = datetime.now() + timedelta(minutes=expire_minutes)
    to_encode.update({"exp": expire, "jti": str(uuid.uuid4())})  
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict):
    expire = datetime.now() + timedelta(days=7)  
    return jwt.encode({"sub": data["sub"], "exp": expire}, settings.SECRET_KEY, algorithm=settings.ALGORITHM)


def verify_access_token(token: str):
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except ExpiredSignatureError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired")
    except InvalidTokenError:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Invalid token")
    except JWTError as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"Token verification failed: {str(e)}")


def is_token_revoked(token: str, db: Session):
    try:
        jti = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])["jti"]
        return db.query(RevokedToken).filter(RevokedToken.jti == jti).first() is not None
    except JWTError:
        return True  
    
def token_jti(token: str):
    try:
        jti = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])["jti"]
        return jti
    except JWTError:
        return None


def revoke_token(token: str, db: Session):
    try:
        jti = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])["jti"]
        db.add(RevokedToken(jti=jti))
        db.commit()
    except JWTError:
        db.rollback() 
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to revoke token")


def generate_verification_token(email: str) -> str:
    """Generate a secure verification token using HMAC."""
    expiry_time = (datetime.now() + timedelta(hours=1)).timestamp()  
    encoded_email = base64.urlsafe_b64encode(email.encode('utf-8')).decode()
    data = f"{encoded_email}|{int(expiry_time)}"  
    signature = hmac.new(settings.SECRET_KEY.encode(), data.encode(), hashlib.sha256).digest()
    token = f"{data}|{base64.urlsafe_b64encode(signature).decode()}"
    return token


def verify_token(token: str) -> str:
    """ Validate the email verification token. """
    try:
        decoded_token = urllib.parse.unquote(token)
        parts = decoded_token.split("|")
        if len(parts) != 3:
            return None  
        encoded_email, expiry, provided_signature = parts[0], int(parts[1]), parts[2]        
        
        if datetime.now().timestamp() > expiry:
            return None  
        email = base64.urlsafe_b64decode(encoded_email.encode()).decode('utf-8')    
        data = f"{encoded_email}|{expiry}"
        expected_signature = hmac.new(settings.SECRET_KEY.encode(), data.encode(), hashlib.sha256).digest()
        expected_signature_encoded = base64.urlsafe_b64encode(expected_signature).decode()

        if not hmac.compare_digest(expected_signature_encoded, provided_signature):
            return None 

        return email  
    except Exception:
        return None  
