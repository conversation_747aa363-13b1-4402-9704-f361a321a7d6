from fastapi import Request
from app.database.session import <PERSON><PERSON>oc<PERSON>
from app.database.models.audit_logs_model import <PERSON>t<PERSON>og
from jose import jwt, JWTError
from app.core.config import settings
from app.services.security_monitor import SecurityMonitor
from user_agents import parse

SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.AL<PERSON><PERSON><PERSON><PERSON><PERSON>

def register_audit_middleware(app):
    @app.middleware("http")
    async def audit_log_middleware(request: Request, call_next):
        response = await call_next(request)

        try:
            db = SessionLocal()
            ip = request.client.host

            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

                role = payload.get("role", "").lower()
                admin_id = payload.get("id")

                if admin_id and role == "admin":
                    user_agent_str = request.headers.get("User-Agent", "")
                    user_agent = parse(user_agent_str)
                    response_body = None
                    if response.status_code >= 400:
                        try:
                            response_body = await response.body()
                        except Exception as e:
                            response_body = str(e)
                    log = AuditLog(
                        admin_id=admin_id,
                        action=f"{request.method} {request.url.path}",
                        ip_address=ip,
                        user_agent = f"{user_agent.browser.family} {user_agent.browser.version_string} on {user_agent.os.family}",
                        status_code = response.status_code,
                        response=response_body
                     
                    )
                    SecurityMonitor.check_for_breach(
                        db=db, admin_id=admin_id, ip=ip, action=f"{request.method} {request.url.path}"
                    )
                    db.add(log)
                    db.commit()
        except JWTError as e:
            print("JWT decode error:", e)
        except Exception as e:
            print("Audit log error:", e)
        finally:
            db.close()

        return response
