from sqlalchemy import Column, Integer, DateTime
from sqlalchemy.sql import func 
from sqlalchemy.dialects.postgresql import <PERSON>SO<PERSON>
from app.database.enum import KeyEnum
from sqlalchemy import Enum, Boolean
from app.database.base import Base


class APIKeysSettings(Base):
    __tablename__ = "api_keys_settings" 

    id = Column(Integer, primary_key=True, index=True)
    key_name = Column(Enum(KeyEnum), unique=True, index=True, nullable=False)
    key_id = Column(JSON, nullable=False)  
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())