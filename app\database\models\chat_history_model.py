from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.database.base import Base

class ChatHistory(Base):
    __tablename__ = "chat_history"
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, ForeignKey("chat_sessions.session_id"), nullable=False)
    chat_id = Column(String, nullable=False)
    role = Column(String, nullable=False)  
    message = Column(String, nullable=False)
    created_at = Column(DateTime, nullable=False)

    session = relationship("ChatSession", back_populates="messages")