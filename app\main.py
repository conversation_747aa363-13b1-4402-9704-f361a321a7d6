from fastapi import FastAPI,HTTPException
from fastapi.middleware.cors import CORSMiddleware
from app.api.routes.user_routes import router as user_router
from app.api.routes.current_user_routes import router as current_user_router
from app.api.routes.oauth_login_routes import router as oauth_router
from app.api.routes.plan_routes import router as plan_router
from app.api.routes.stripe_payment_routes import router as stripe_payment_router
from app.api.routes.dashboad_routes import router as dashboard_router
from app.api.routes.payment_management_routes import router as payment_management_router
from app.api.routes.prd_upload_routes import router as prd_upload_router
from app.api.routes.prd_chat_history_chat import router as prd_chat_history_router
from app.api.routes.notion_oauth_routes import router as notion_oauth_router
from app.api.routes.security_routes import router as security_router
from app.api.routes.content_page_routes import router as content_page_router
from app.api.routes.confluence_oauth_routes import router as confluence_oauth_router
from app.api.routes.api_keys_settings_routes import router as api_keys_settings_router
from app.core.config import settings
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from fastapi.openapi.utils import get_openapi
from fastapi.staticfiles import StaticFiles
from app.core.middleware.audit_logs import register_audit_middleware

app = FastAPI()

register_audit_middleware(app)

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,            
    allow_credentials=True,           
    allow_methods=["*"],              
    allow_headers=["*"],              
)

app.mount("/static", StaticFiles(directory="static"), name="static")

security = HTTPBearer()
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema    
    openapi_schema = get_openapi(
        title="PRD API",
        version="1.0.0",        
        routes=app.routes,
    )  
    if "components" in openapi_schema:
        if "securitySchemes" in openapi_schema["components"]:
            openapi_schema["components"]["securitySchemes"]["HTTPBearer"] = {
                "type": "http",
                "scheme": "Bearer",
                "bearerFormat": "JWT",
                "description": "Enter **only** the token. Do not add 'Bearer '."
            }
    app.openapi_schema = openapi_schema
    return app.openapi_schema
app.openapi = custom_openapi


app.include_router(user_router, prefix=f"{settings.API_V1_STR}/auth", tags=["Authentication"])
app.include_router(oauth_router, prefix=f"{settings.API_V1_STR}/OAuth", tags=["OAuth"])
app.include_router(notion_oauth_router, prefix=f"{settings.API_V1_STR}/notion", tags=["Notion_OAuth"])
app.include_router(confluence_oauth_router, prefix=f"{settings.API_V1_STR}/confluence", tags=["Confluence_OAuth"])
app.include_router(dashboard_router, prefix=f"{settings.API_V1_STR}/dashboard", tags=["Dashboard"])
app.include_router(current_user_router, prefix=f"{settings.API_V1_STR}/user", tags=["User"])
app.include_router(plan_router, prefix=f"{settings.API_V1_STR}/plan", tags=["Subscription_Plans"])
app.include_router(stripe_payment_router, prefix=f"{settings.API_V1_STR}/stripe_payment", tags=["Payment_Checkout"])
app.include_router(payment_management_router, prefix=f"{settings.API_V1_STR}/payment_management", tags=["Payment_Management"])
app.include_router(prd_upload_router, prefix=f"{settings.API_V1_STR}/prd_upload", tags=["PRD_Upload"])
app.include_router(prd_chat_history_router, prefix=f"{settings.API_V1_STR}/prd_chat_history", tags=["PRD_Chat_History"])
app.include_router(security_router, prefix=f"{settings.API_V1_STR}/security", tags=["Security"])
app.include_router(content_page_router, prefix=f"{settings.API_V1_STR}/content_page", tags=["Content_Page"])
app.include_router(api_keys_settings_router, prefix=f"{settings.API_V1_STR}/api_keys_settings", tags=["API_Keys_Settings"])


@app.get("/")
def root():
    return {"message": "Welcome to PRD API"}

@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request, exc):
    return JSONResponse(status_code=exc.status_code,content={"message": exc.detail})