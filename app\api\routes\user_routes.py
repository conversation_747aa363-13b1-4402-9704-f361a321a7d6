from fastapi import APIRouter, Depends, BackgroundTasks,HTTPException,status
from sqlalchemy.orm import Session
from app.database.session import get_db
from app.schemas.user_schema import UserCreate,UserLogin,ForgotPassword,ResetPassword,UpdatePassword
from app.services.user_service import Authentication
from app.database.dependencies import get_current_user
from app.core.security import verify_access_token
router = APIRouter()

@router.post("/register")
def register_user(user_data: UserCreate,background_tasks: BackgroundTasks,db: Session = Depends(get_db)):
    if user_data.role == "admin" and not user_data.token:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin registration requires authorization")    
    
    if user_data.token:
        decryted_token = verify_access_token(user_data.token)
        role: str = decryted_token.get("role")        
        if role == "Admin" and user_data.role == "Admin":
            return Authentication.register_user_service(user_data, background_tasks, db)
        elif user_data.role == "User" and role == "Admin":
            return Authentication.register_user_service(user_data, background_tasks, db)
        else:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access forbidden")            
    else:              
        return Authentication.register_user_service(user_data, background_tasks, db)

@router.get("/verify-email")
def verify_email(token: str, db: Session = Depends(get_db)):
    return Authentication.verify_email_service(token, db)

@router.post("/login")
def login_user(user_data: UserLogin, db: Session = Depends(get_db)):
    return Authentication.login_user_service(user_data, db)

@router.post("/logout")
def logout_user(current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    return Authentication.logout_user_service(current_user,db)

@router.post("/forgot_password")
def forgot_password(request: ForgotPassword,background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    return Authentication.forgot_password_service(request,background_tasks, db)

@router.post("/reset_password")
def reset_password(request: ResetPassword,  db: Session = Depends(get_db)):
    return Authentication.reset_password_service(request, db)

@router.post("/update_password")
def update_password(request:UpdatePassword,current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return Authentication.update_password_service(request,current_user,db)

@router.post("/get_new_access_token")
def get_new_access_token(refresh_token: str, db: Session = Depends(get_db)):
    return Authentication.get_new_access_token_service(refresh_token, db)
