"""remove column

Revision ID: b9d8fb2f529a
Revises: 2ef60a9d7e8f
Create Date: 2025-06-30 14:45:39.064865

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b9d8fb2f529a'
down_revision: Union[str, None] = '2ef60a9d7e8f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('notion_tokens', 'user_selected_page_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notion_tokens', sa.Column('user_selected_page_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
