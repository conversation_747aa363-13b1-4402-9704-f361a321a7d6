import stripe
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException,status
from sqlalchemy.future import select                            
from app.database.models.plan_model import Plan
from app.database.models.subscriptions_model import Subscription
from app.schemas.plan_schema import PlanCreate,PlanUpdate
from datetime import datetime
from app.core.config import settings
from sqlalchemy import func,asc,desc


class PlanService:
    @staticmethod
    async def create_plan_service(plan_data: PlanCreate, session: AsyncSession):
        
        stripe.api_key = settings.STRIPE_SECRET_KEY
        if not plan_data.plan_name:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Plan name is required")
        if plan_data.price is None:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Price is required")
        if not plan_data.uploads:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Uploads is required")
        if not plan_data.features:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Features is required")
        if plan_data.duration is None:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Duration is required")        
        existing_plan = session.execute(
            select(Plan).filter_by(plan_name=plan_data.plan_name, price=plan_data.price)
        ).scalar()
        if existing_plan:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Plan with this name already exists")
        if plan_data.price == 0:
            free_plan = session.execute(select(Plan).filter_by(price=0)).scalar()
            if free_plan:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Only one free plan is allowed")
            plan_data.stripe_price_id = "none"
        else:
            stripe_product = stripe.Product.create(
                name=plan_data.plan_name,
                description=plan_data.description
            )
            stripe_price = stripe.Price.create(
                product=stripe_product.id,
                unit_amount=int(plan_data.price * 100),
                currency="usd",
                recurring={
                    "interval": "day",
                    "interval_count": plan_data.duration
                } if plan_data.duration else None
            )
            plan_data.stripe_price_id = stripe_price.id
        new_plan = Plan(**plan_data.dict())
        new_plan.plan_status = True
        new_plan.created_at = datetime.now()
        new_plan.updated_at = datetime.now()
        session.add(new_plan)
        session.commit()
        session.refresh(new_plan)
        return {"message": "Plan created successfully"}
    
    @staticmethod
    async def update_plan_service(plan_data: PlanUpdate, session: AsyncSession):

        stripe.api_key = settings.STRIPE_SECRET_KEY 
        if plan_data.price == 0 and plan_data.plan_status == False:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Free plan cannot be deactivated")
        result = session.execute(select(Plan).where(Plan.id == plan_data.id))
        existing_plan = result.scalars().first()
        if not existing_plan:
            raise HTTPException(status_code=204, detail="Plan not found")
        if existing_plan.price == 0 and plan_data.price != 0:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Free plan price cannot be changed")
        if not plan_data.plan_name:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Plan name is required")
        if (existing_plan.price != 0 and (not plan_data.price or plan_data.price < 0)):
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Price is required")
        if not plan_data.uploads or plan_data.uploads <= 0:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Uploads is required")
        if not plan_data.features or len(plan_data.features) == 0:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Features are required")
        if plan_data.duration is None:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Duration is required")      

        # Only retrieve price from Stripe if it's not a free plan
        if existing_plan.stripe_price_id != "none":
            price = stripe.Price.retrieve(existing_plan.stripe_price_id)
            product_id = price.product
            
            if plan_data.plan_name != existing_plan.plan_name or plan_data.description != existing_plan.description:
                stripe.Product.modify(
                    product_id,  
                    name=plan_data.plan_name,
                    description=plan_data.description
                )

            if plan_data.price != existing_plan.price or plan_data.duration != existing_plan.duration:
                new_stripe_price = stripe.Price.create(
                    product=product_id, 
                    unit_amount=int(plan_data.price * 100),
                    currency="usd",
                    recurring={
                        "interval": "day",
                        "interval_count": plan_data.duration
                    }
                )
                existing_plan.stripe_price_id = new_stripe_price.id 

        existing_plan.plan_name = plan_data.plan_name
        existing_plan.description = plan_data.description
        existing_plan.uploads = plan_data.uploads
        existing_plan.features = plan_data.features
        existing_plan.price = plan_data.price
        existing_plan.duration = plan_data.duration
        existing_plan.plan_status = plan_data.plan_status
        existing_plan.updated_at = datetime.now()

        session.commit()
        return {"message": "Plan updated successfully"}
    
    @staticmethod
    def delete_plan_service(plan_id: int, session: AsyncSession):

        stripe.api_key = settings.STRIPE_SECRET_KEY 
        try:
            result = session.execute(select(Plan).where(Plan.id == plan_id))
            existing_plan = result.scalars().first()

            if not existing_plan:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Plan not found")
            
            # # Prevent deletion of free plans
            # if existing_plan.price == 0:
            #     raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, 
            #                        detail="Free plans cannot be deleted")
                
            sub_result = session.execute(select(Subscription).filter_by(plan_id=plan_id))
            existing_subscriptions = sub_result.scalars().all()

            if existing_subscriptions:
                raise HTTPException(
                    status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, 
                    detail="This plan cannot be deleted as it is currently assigned to active subscriptions.Deactivate the Plan instead."
                )
                
            # Only attempt to archive Stripe product if it's not a free plan
            if existing_plan.stripe_price_id != "none":
                try:
                    price = stripe.Price.retrieve(existing_plan.stripe_price_id)
                    product_id = price.product
                    stripe.Product.modify(product_id, active=False)
                except Exception as e:
                    raise HTTPException(status_code=500, detail=f"Failed to archive Stripe product: {str(e)}")

            session.delete(existing_plan)
            session.commit()

            return {"message": "Plan deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:           
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

    @staticmethod
    def get_plan_service(plan_id: int, session: AsyncSession):
        result =  session.execute(select(Plan).filter_by(id=plan_id))
        plan = result.scalars().first()
        if not plan:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Plan not found")
        return plan
        
    @staticmethod
    async def get_all_plans_service(
                        current_user,
                        session: AsyncSession,
                        page: int,
                        page_size: int,
                        search: str = None,
                        is_active: bool = None,
                        start_date: datetime = None,
                        end_date: datetime = None,
                        sort_by: str = "created_at",
                        sort_order: str = "desc",
                        
                ):
        offset = (page - 1) * page_size
        if current_user.role.name == "Admin":
            base_query = select(Plan)
        else:
            base_query = select(Plan).where(Plan.plan_status == True)              
        conditions = []
        if search:
            conditions.append(Plan.plan_name.ilike(f"%{search}%"))
        if is_active is not None:
            conditions.append(Plan.plan_status == is_active)
        if start_date and end_date:
            conditions.append(Plan.created_at.between(start_date, end_date))
        elif start_date:
            conditions.append(Plan.created_at >= start_date)
        elif end_date:
            conditions.append(Plan.created_at <= end_date)
        if conditions:
            base_query = base_query.where(*conditions)        
        total_query = session.execute(
            select(func.count()).select_from(base_query.subquery())
        )
        total_records = total_query.scalar() or 0   
        
        existing_subscription = session.execute(select(Subscription).filter_by(user_id=current_user.id)).scalar()
        subscribed_plan_id = existing_subscription.plan_id if existing_subscription else None

        sort_column = {
            "name": Plan.plan_name,
            "price": Plan.price,
            "created_at": Plan.created_at
        }.get(sort_by, Plan.price)
        sort_direction = asc(sort_column) if sort_order == "asc" else desc(sort_column)        
        query = base_query.order_by(sort_direction).offset(offset).limit(page_size)
        result = session.execute(query)
        plans = result.scalars().all()        
   
        plans_with_subscription = []
        has_subscription = subscribed_plan_id is not None
        
        for plan in plans:           
            
            is_subscribed = False
            
            if subscribed_plan_id and plan.id == subscribed_plan_id and existing_subscription.expiry_date > datetime.now() :              
                is_subscribed = True
            elif not has_subscription and plan.price == 0:               
                is_subscribed = True            
            plan_dict = {
                "id": plan.id,
                "plan_name": plan.plan_name,
                "description": plan.description,
                "uploads": plan.uploads,
                "features": plan.features,
                "price": plan.price,
                "duration": plan.duration,
                "plan_status": plan.plan_status,
                "is_subscribed": is_subscribed
            }
            plans_with_subscription.append(plan_dict)
            
        return {
            "total": total_records,
            "total_pages": (total_records + page_size - 1) // page_size,
            "plans": plans_with_subscription
        }
