from fastapi import APIRouter, Depends,Query
from datetime import date
from sqlalchemy.orm import Session
from app.database.session import get_db
from app.database.dependencies import get_current_user,role_required
from app.services.current_user_service import CurrentUserService
from app.schemas.user_schema import UserResponse,UpdateResponse,UpdateStatus
from fastapi import UploadFile
from typing import Optional

router = APIRouter()

@router.get("/get_user",response_model=UserResponse)
def get_user(current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    return CurrentUserService.get_user_service(current_user, db)

@router.get("/get_all_users")
def get_all_users(
    current_user=Depends(role_required(["Admin"])),
    db: Session = Depends(get_db),
    page: int = Query(1, description="Page number (starts from 1)"),
    page_size: int = Query(10, description="Number of records per page"),
    search: str = Query(None, description="Search by name or email"),
    is_active: bool = Query(None, description="Filter by active status (true/false)"),
    start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
    end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD"),
    sort_by: str = Query("name", description="Sort by column (id, name, email, created_at)"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)")
):
    data = CurrentUserService.get_all_users_service(
        db, page, page_size, search, is_active, start_date, end_date, sort_by, sort_order
    )

    return {
        "total_records": data["total"],
        "total_pages": data["total_pages"],
        "items": [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "last_login": user.last_login,
                "created_at": user.created_at,
                "profile_picture": user.profile_picture,
                "is_active": user.is_active,
                "is_deleted": user.is_deleted
            } for user in data["items"]
        ]
    }

@router.put("/update_user")
def update_user(request:UpdateResponse,current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return CurrentUserService.update_user_service(request,db)

@router.put("/update_user_status")
def update_user_status(request:UpdateStatus,current_user=Depends(role_required(["Admin"])),db:Session =Depends(get_db)):
    return CurrentUserService.update_user_status_service(request,db)

@router.delete("/delete_user/{user_id}")
def delete_user(user_id:int,current_user=Depends(role_required(["Admin"])),db:Session =Depends(get_db)):
    return CurrentUserService.delete_user_service(user_id,db)

@router.post("/upload_profile_picture")
def upload_profile_picture(file:UploadFile,current_user=Depends(get_current_user),db:Session = Depends(get_db)):
    return CurrentUserService.upload_profile_picture_service(file,current_user,db)

@router.get("/get_user_uploads")
def get_user_uploads(current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return CurrentUserService.get_user_uploads_service(current_user,db)


@router.get("/get_user_data")
def get_user_data( user_id: int, page: int = 1,page_size: int = 10,sort_by: str = "created_at",sort_order: str = "desc",search: Optional[str] = None,current_user=Depends(role_required(["Admin"])), db: Session = Depends(get_db)):
    return CurrentUserService.get_user_data_service(
        user_id, db, page, page_size, sort_by, sort_order, search
    )

@router.get("/get_user_billing_details")
def get_user_billing_details(current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return CurrentUserService.get_user_billing_details_service(current_user,db)

@router.delete("/delete_user_billing_details")
def delete_user_billing_details(current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return CurrentUserService.delete_user_billing_details_service(current_user,db)


@router.post("/delete_request_submission")
def delete_request_submission(reason:str,current_user=Depends(get_current_user),db:Session =Depends(get_db)):
    return CurrentUserService.delete_request_data_service(reason,current_user,db)

@router.get("/view_deletion_request")
def get_deletion_request(userid:int,current_user=Depends(role_required(["Admin"])),db:Session =Depends(get_db)):
    return CurrentUserService.get_deletion_request_service(userid,db)