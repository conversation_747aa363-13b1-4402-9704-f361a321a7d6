from fastapi import APIRouter, Depends
from app.database.session import get_db
from sqlalchemy.orm import Session
from app.services.oauth_login_service import OAuthLoginService

router=APIRouter()


@router.post("/auth/google_login")
def google_login_router(db:Session=Depends(get_db)):
    return OAuthLoginService.google_login_service(db)

@router.get("/auth/callback")
async def google_callback_router(code:str,db:Session=Depends(get_db)):
    return await OAuthLoginService.google_callback_service(code,db)

@router.post("/auth/github_login")
def github_login_router(db:Session=Depends(get_db)):
    return OAuthLoginService.github_login_service(db)

@router.get("/auth/github_callback")
async def github_callback_router(code:str,db:Session=Depends(get_db)):
    return await OAuthLoginService.github_callback_service(code,db)

# @router.post("/auth/microsoft_login")
# def microsoft_login_router():
#     return OAuthLoginService.microsoft_login_service()

# @router.get("/auth/microsoft_callback")
# async def microsoft_callback_router(code:str,db:Session=Depends(get_db)):
#     return await OAuthLoginService.microsoft_callback_service(code,db)