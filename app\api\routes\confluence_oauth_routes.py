from fastapi import APIRouter, Request, Depends, HTTPException
import json
import base64
from app.services.confluence_oauth_service import ConfluenceOAuthService
from app.database.session import get_db
from sqlalchemy.orm import Session
from app.database.dependencies import get_current_user
from app.database.models.user_model import User
from app.database.models.atlassian_tokens_model import Atlassian<PERSON>oken
from datetime import datetime, timedelta
from app.schemas.confluence_schema import CreatePageRequest,UpdatePageRequest


router = APIRouter()



@router.get("/confluence_oauth")
def start_oauth(current_user=Depends(get_current_user), db: Session = Depends(get_db)):
    """Start OAuth flow for Confluence"""
    url = ConfluenceOAuthService.get_authorization_url(current_user)
    return {"url": url}

@router.get("/confluence_callback")
async def oauth_callback(request: Request, db: Session = Depends(get_db)):
    """Handle OAuth callback from Confluence"""
    code = request.query_params.get("code")
    state = request.query_params.get("state")    
    if not code:
        raise HTTPException(status_code=400, detail="Missing authorization code")    
    if not state:
        raise HTTPException(status_code=400, detail="Missing state parameter")
    try:
        decoded_state = json.loads(base64.b64decode(state).decode())
        user_id = decoded_state["user_id"]
    except (ValueError, KeyError, json.JSONDecodeError):
        raise HTTPException(status_code=400, detail="Invalid state parameter")
    try:
        token_data = await ConfluenceOAuthService.exchange_code_for_token(code)
        access_token = token_data["access_token"]
        refresh_token = token_data["refresh_token"]
        expires_in = token_data["expires_in"]
        expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
        cloud_id = await ConfluenceOAuthService.get_cloud_id(access_token)
        existing = db.query(AtlassianToken).filter_by(user_id=user_id).first()
        if existing:
            existing.access_token = access_token
            existing.refresh_token = refresh_token
            existing.expires_at = expires_at
            existing.cloud_id = cloud_id
            existing.updated_at = datetime.utcnow() 
        else:
            token = AtlassianToken(
                user_id=user_id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                cloud_id=cloud_id  
            )
            db.add(token)
        db.commit()
        return {"message": "Authorization successful"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"OAuth callback failed: {str(e)}")

@router.get("/confluence_spaces")
async def get_spaces(current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    """Get all accessible Confluence spaces"""
    try:        
        spaces = await ConfluenceOAuthService.get_confluence_spaces(
            current_user.id, db
        )
        return spaces
    except ValueError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch spaces: {str(e)}")

@router.post("/confluence_create_page")
async def create_page_v1(request: CreatePageRequest, current_user=Depends(get_current_user), db: Session = Depends(get_db)):
    """Create page using v1 API"""
    try:
        page = await ConfluenceOAuthService.create_confluence_page_with_debug(
            current_user.id, 
            db, 
            request.cloud_id,
            request.space_id,
            request.title,
            request.content
        )
        return {
            "message": "Page created successfully with v1 API",
            "page_id": page["id"],
            "page_url": f"{page['_links']['base']}{page['_links']['webui']}"
        }
    except HTTPException:
        raise  # Re-raise HTTPExceptions as they are
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create page: {str(e)}")
    


@router.put("/confluence_update_page")
async def update_page(request: UpdatePageRequest,current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    """Update an existing Confluence page"""
    try:
        page = await ConfluenceOAuthService.update_confluence_page(
            current_user.id,
            db,
            request.cloud_id,
            request.page_id,
            request.title,
            request.content,
            request.version
        )
        return {
            "message": "Page updated successfully",
            "page_id": page["id"],
            "page_url": page["_links"]["webui"]
        }
    except ValueError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update page: {str(e)}")

@router.get("/confluence_auth_status")
async def check_auth_status(current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    """Check if user has valid Confluence authorization"""
    token_record = db.query(AtlassianToken).filter_by(user_id=current_user.id).first()
    
    if not token_record:
        return {"authenticated": False, "message": "No Confluence authorization found"}
    
    if datetime.utcnow() >= token_record.expires_at:
        return {"authenticated": False, "message": "Authorization expired"}
    
    return {
        "authenticated": True, 
        "expires_at": token_record.expires_at,
        "cloud_id": getattr(token_record, 'cloud_id', None)
    }

@router.delete("/confluence_revoke")
async def revoke_authorization(current_user=Depends(get_current_user),db: Session = Depends(get_db)):
    """Revoke Confluence authorization"""
    token_record = db.query(AtlassianToken).filter_by(user_id=current_user.id).first()
    
    if token_record:
        db.delete(token_record)
        db.commit()
    
    return {"message": "Authorization revoked successfully"}