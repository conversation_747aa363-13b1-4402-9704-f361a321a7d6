from pydantic import BaseModel, Field
from typing import Optional, List


class PlanCreate(BaseModel):
    plan_name: Optional[str] = None
    price: Optional[float] = None
    uploads: Optional[int] = None
    features: List[str] = None
    duration: Optional[int] = None
    description: Optional[str] =   None
    stripe_price_id: Optional[str] = None
    plan_status: Optional[bool] = Field(default=True)

    class Config:
        from_attributes = True

class PlanUpdate(PlanCreate):
    id: int
    
