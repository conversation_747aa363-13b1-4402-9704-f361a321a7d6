from fastapi import APIRouter, Depends,Query
from datetime import date
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.session import get_db
from app.services.plan_service import PlanService
from app.schemas.plan_schema import PlanCreate,PlanUpdate
from sqlalchemy.orm import Session
from app.database.dependencies import role_required,get_current_user

router = APIRouter()

@router.post("/create_plan")
async def create_plan(plan_data: PlanCreate, current_user=Depends(role_required(["Admin"])), db: AsyncSession = Depends(get_db)):
    return await PlanService.create_plan_service(plan_data, db)

@router.put("/update_plan")
async def update_plan(plan_data:PlanUpdate ,current_user=Depends(role_required(["Admin"])),db:AsyncSession = Depends(get_db)):
    return await PlanService.update_plan_service(plan_data,db)

@router.delete("/delete_plan/{plan_id}")
def delete_plan(plan_id:int,current_user=Depends(role_required(["Admin"])),db:AsyncSession = Depends(get_db)):
    return  PlanService.delete_plan_service(plan_id,db)

@router.get("/get_plan")
def get_plan(plan_id:int,session:AsyncSession = Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return  PlanService.get_plan_service(plan_id,session)

@router.get("/get_all_plans")
async def get_all_plans(current_user=Depends(get_current_user),
                db: AsyncSession = Depends(get_db),
                page: int = Query(1, description="Page number (starts from 1)"),
                page_size: int = Query(10, description="Number of records per page"),
                search: str = Query(None, description="Search by name or email"),
                is_active: bool = Query(None, description="Filter by active status (true/false)"),
                start_date: date = Query(None, description="Filter users created after this date YYYY-MM-DD"),
                end_date: date = Query(None, description="Filter users created before this date YYYY-MM-DD"),
                sort_by: str = Query("name", description="Sort by column (id, name, email, created_at)"),
                sort_order: str = Query("desc", description="Sort order (asc/desc)")):
    data = await PlanService.get_all_plans_service(current_user,db,page,page_size,search,is_active,start_date,end_date,sort_by,sort_order)
    return{
        "total_records": data["total"],
        "total_pages": data["total_pages"],
        "items": data["plans"]  
    }
