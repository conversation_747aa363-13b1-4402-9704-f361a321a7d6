from sqlalchemy.orm import Session
from app.database.models.user_model import User
from app.database.models.payment_model import Payment
from app.database.models.subscriptions_model import Subscription
from datetime import datetime, time 
from sqlalchemy import extract, func
import calendar
from calendar import monthrange
from collections import OrderedDict
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from app.database.models.chat_session_model import ChatSession
from app.database.models.chat_history_model import ChatHistory
from datetime import date
from typing import Optional


class DashboardService:

    @staticmethod
    def get_active_users_service(db: Session,start_date: Optional[date] = None, end_date: Optional[date] = None):
        query = db.query(User).filter(User.is_active == True)
        if start_date:
            query = query.filter(User.created_at >= start_date)
        if end_date:
            query = query.filter(User.created_at <= end_date + timedelta(days=1))
        active_users = query.count()
        return {"active_users": active_users}


    @staticmethod
    def get_total_revenue_service(db: Session, start_date: Optional[date] = None, end_date: Optional[date] = None):
        query = db.query(Payment).filter(Payment.payment_status == "succeeded")

        if start_date:
            query = query.filter(Payment.created_at >= start_date)
        if end_date:
            query = query.filter(Payment.created_at <= end_date + timedelta(days=1))

        total_revenue = sum([payment.amount for payment in query.with_entities(Payment.amount).all()])
        return {"total_revenue": total_revenue}
    
    @staticmethod
    def get_active_subscriptions_service(db: Session,start_date: Optional[date] = None, end_date: Optional[date] = None):
        query = db.query(Subscription).filter(Subscription.expiry_date > datetime.now())
        if start_date:
            query = query.filter(Subscription.created_at >= start_date)
        if end_date:
            query = query.filter(Subscription.created_at <= end_date + timedelta(days=1))
        active_subscriptions = query.count()
        return {"active_subscriptions": active_subscriptions}
    
    @staticmethod
    def get_total_prd_generated(db: Session,start_date: Optional[date] = None, end_date: Optional[date] = None):
        query = db.query(ChatSession).join(ChatHistory).filter(ChatHistory.role == "ai")
        if start_date:
            query = query.filter(ChatSession.created_at >= start_date)
        if end_date:
            query = query.filter(ChatSession.created_at <= end_date + timedelta(days=1))
        total_prd_generated = query.count()
        return {"total_prd_generated": total_prd_generated}
    
    @staticmethod
    def get_user_month_wise_data(db: Session, start_date=None, end_date=None):
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if not start_date and not end_date:
            first_of_current_month = today.replace(day=1)
            filter_date = (first_of_current_month - relativedelta(months=11))
            last_day = monthrange(today.year, today.month)[1]
            end_filter_date = today.replace(day=last_day, hour=23, minute=59, second=59)
        else:
            if start_date:
                filter_date = datetime.combine(start_date, time.min)
            else:
                filter_date = datetime.min

            if end_date:
                end_filter_date = datetime.combine(end_date, time.max)
            else:
                end_filter_date = today
        months_list = []
        current = filter_date.replace(day=1)
        while current <= end_filter_date:
            months_list.append((current.year, current.month))
            current = current + relativedelta(months=1)
        if not months_list and filter_date.year == end_filter_date.year and filter_date.month == end_filter_date.month:
            months_list.append((filter_date.year, filter_date.month))
        user_counts = (
            db.query(
                extract("year", User.created_at).label("year"),
                extract("month", User.created_at).label("month"),
                func.count(User.id).label("total_users")
            )
            .filter(
                User.created_at >= filter_date,
                User.created_at <= end_filter_date
            )
            .group_by("year", "month")
            .order_by("year", "month")
            .all()
        )
        users_per_month = {
            f"{calendar.month_abbr[int(month)]} {year}": total
            for year, month, total in user_counts
        }
        final_result = OrderedDict()
        for year, month in months_list:
            key = f"{calendar.month_abbr[month]} {year}"
            final_result[key] = users_per_month.get(key, 0)
        return {"users_by_month": final_result}

    
    @staticmethod
    def get_payment_month_wise_data(db: Session, start_date=None, end_date=None):    
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if not start_date and not end_date:           
            first_of_current_month = today.replace(day=1)
            filter_date = (first_of_current_month - relativedelta(months=11))
            last_day = monthrange(today.year, today.month)[1]
            end_filter_date = today.replace(day=last_day, hour=23, minute=59, second=59)
        else:
            filter_date = start_date if start_date else datetime.min
            end_filter_date = end_date if end_date else today        
    
        months_list = []
        current = filter_date.replace(day=1)
        while current <= end_filter_date:
            months_list.append((current.year, current.month))
            current = current + relativedelta(months=1)
        
        if not months_list and filter_date.year == end_filter_date.year and filter_date.month == end_filter_date.month:
            months_list.append((filter_date.year, filter_date.month))
        
        payment_counts = (
            db.query(
               extract("year", Payment.created_at).label("year"),
               extract("month", Payment.created_at).label("month"),
               func.sum(Payment.amount).label("total_amount")
            )
            .filter(
                Payment.created_at >= filter_date,
                Payment.created_at <= end_filter_date,
                Payment.payment_status == "succeeded"  
            )  
            .group_by("year", "month")
            .order_by("year", "month")
            .all()
        )    
        payments_per_month = {
            f"{calendar.month_abbr[int(month)]} {year}": (float(total) if total is not None else 0)  
            for year, month, total in payment_counts
        } 
        final_result = OrderedDict()
        for year, month in months_list:
            key = f"{calendar.month_abbr[month]} {year}" 
            final_result[key] = payments_per_month.get(key, 0)
        
        return {"payments_by_month": final_result}
    
    
    @staticmethod
    def get_active_or_inactive_subscrptions(db: Session, start_date=None, end_date=None):
        today = datetime.now() 
        chart_data = OrderedDict()
        
        if start_date and end_date:
            filter_start = start_date
            filter_end = end_date

            months_list = []
            current = filter_start.replace(day=1)
            while current <= filter_end:
                months_list.append(current)
                current = current + relativedelta(months=1)
        else:
            months_list = []
            for i in range(12):
                month_date = (today.replace(day=1) - timedelta(days=i * 30)).replace(day=1)
                months_list.append(month_date)
            months_list.reverse()
        
        for month_date in months_list:
            month_start = month_date.replace(day=1)
            month_end = (month_start + relativedelta(months=1)) - timedelta(seconds=1)
            month_name = month_start.strftime("%b %Y")
 
            active_query = db.query(func.count()).filter(
                Subscription.created_at <= month_end,  
                Subscription.expiry_date >= month_start 
            )
            
            if start_date:
                active_query = active_query.filter(Subscription.created_at >= start_date)
            if end_date:
                active_query = active_query.filter(Subscription.created_at <= end_date)
            active_count = active_query.scalar()
 
            new_query = db.query(func.count()).filter(
                Subscription.created_at >= month_start,
                Subscription.created_at <= month_end
            )
            if start_date:
                new_query = new_query.filter(Subscription.created_at >= start_date)
            if end_date:
                new_query = new_query.filter(Subscription.created_at <= end_date)
            new_count = new_query.scalar()
 
            inactive_query = db.query(func.count()).filter(
                Subscription.expiry_date < month_start
            )
            if start_date:
                inactive_query = inactive_query.filter(Subscription.created_at >= start_date)
            if end_date:
                inactive_query = inactive_query.filter(Subscription.created_at <= end_date)
            inactive_count = inactive_query.scalar()
 
            chart_data[month_name] = {
                "monthly subscription": active_count,
                "new subscription": new_count,
                "inactive subs": inactive_count
            }
 
        return {"subscription_status_chart": chart_data}
    
    @staticmethod
    def get_prd_generated_month_wise_data(db: Session, start_date=None, end_date=None):
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if not start_date and not end_date:
            first_of_current_month = today.replace(day=1)
            filter_date = (first_of_current_month - relativedelta(months=11))
            last_day = monthrange(today.year, today.month)[1]
            end_filter_date = today.replace(day=last_day, hour=23, minute=59, second=59)
        else:
            if start_date:
                filter_date = datetime.combine(start_date, time.min)
            else:
                filter_date = datetime.min

            if end_date:
                end_filter_date = datetime.combine(end_date, time.max)
            else:
                end_filter_date = today
                
        months_list = []
        current = filter_date.replace(day=1)
        while current <= end_filter_date:
            months_list.append((current.year, current.month))
            current = current + relativedelta(months=1)
        
        if not months_list and filter_date.year == end_filter_date.year and filter_date.month == end_filter_date.month:
            months_list.append((filter_date.year, filter_date.month))
        
        prd_counts = (
            db.query(
                extract("year", ChatHistory.created_at).label("year"),
                extract("month", ChatHistory.created_at).label("month"),
                func.count(ChatHistory.id).label("total_prd")
            )
            .filter(
                ChatHistory.created_at >= filter_date,
                ChatHistory.created_at <= end_filter_date,
                ChatHistory.role == "ai"
            )
            .group_by("year", "month")
            .order_by("year", "month")
            .all()
        )
        
        prd_per_month = {
            f"{calendar.month_abbr[int(month)]} {year}": total
            for year, month, total in prd_counts
        }
        
        final_result = OrderedDict()
        for year, month in months_list:
            key = f"{calendar.month_abbr[month]} {year}"
            final_result[key] = prd_per_month.get(key, 0)
        
        return {"prd_generated_by_month": final_result}





