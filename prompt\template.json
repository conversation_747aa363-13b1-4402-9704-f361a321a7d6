{"name": null, "input_variables": ["extracted_text", "persona", "user_instruction"], "optional_variables": [], "output_parser": null, "partial_variables": {}, "metadata": null, "tags": null, "template": "\n{persona}\n\nYou are tasked with generating a structured and professional Project Requirement Document (PRD) based on the extracted information and any optional user instructions provided.\n\nExtracted Input:\n{extracted_text}\n\nAdditional User Instructions:\n{user_instruction}\n\nThe PRD should include the following sections:\n1. Project Title  \n2. Project Overview  \n3. Business Objectives  \n4. Functional Requirements  \n5. Non-Functional Requirements  \n6. Stakeholders  \n7. Timeline and Milestones  \n8. Assumptions and Constraints  \n9. Risks and Mitigation  \n10. Conclusion or Recommendations\n\nBe concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "template_format": "f-string", "validate_template": true, "_type": "prompt"}