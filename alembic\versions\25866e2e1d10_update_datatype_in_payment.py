"""Update datatype in payment

Revision ID: 25866e2e1d10
Revises: 5dfb367ba41d
Create Date: 2025-04-28 13:17:00.297749

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '25866e2e1d10'
down_revision: Union[str, None] = '5dfb367ba41d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'payment_status',
               existing_type=sa.BOOLEAN(),
               type_=sa.String(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'payment_status',
               existing_type=sa.String(),
               type_=sa.BOOLEAN(),
               existing_nullable=False)
    # ### end Alembic commands ###
