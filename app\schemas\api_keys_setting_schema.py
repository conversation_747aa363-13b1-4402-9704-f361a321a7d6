from pydantic import BaseModel
from app.database.enum import Key<PERSON><PERSON>
from typing import Union
import json
from datetime import datetime


class APIKeyBase(BaseModel):
    key_name: KeyEnum
    key_id: Union[dict, str]  

    def serialize_key_id(self):
        if isinstance(self.key_id, str):
            return json.loads(self.key_id)  
        return self.key_id

class APIKeyCreate(APIKeyBase):
    pass  

class APIKeyUpdate(BaseModel):
    id: int
    key_name: KeyEnum
    key_id: Union[dict, str]

class APIKeyResponse(APIKeyBase):
    id: int
    created_at: datetime  
    updated_at: datetime  

    class Config:
        from_attributes = True  
        json_encoders = {
            datetime: lambda v: v.isoformat()  
        }