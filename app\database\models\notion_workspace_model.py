from sqlalchemy import Column, String,ForeignKey
from app.database.base import Base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSON


class NotionToken(Base):
    __tablename__ = "notion_tokens"

    workspace_id = Column(String, primary_key=True)
    access_token = Column(String, nullable=True)
    bot_id = Column(String,nullable=True)
    duplicated_template_id = Column(String,nullable=True)
    user_id = Column(ForeignKey("users.id"), nullable=False) 

    user = relationship("User", backref="notion_tokens")

