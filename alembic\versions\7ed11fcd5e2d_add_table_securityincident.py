"""add table securityIncident

Revision ID: 7ed11fcd5e2d
Revises: 8a303a99313b
Create Date: 2025-06-23 12:09:39.499278

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7ed11fcd5e2d'
down_revision: Union[str, None] = '8a303a99313b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('security_incidents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('admin_id', sa.Integer(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.<PERSON>umn('action', sa.String(), nullable=True),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_security_incidents_id'), 'security_incidents', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_security_incidents_id'), table_name='security_incidents')
    op.drop_table('security_incidents')
    # ### end Alembic commands ###
