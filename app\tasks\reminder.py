from app.celery_app import celery_app
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app.core.config import settings



@celery_app.task
def send_subscription_reminder_email(user_id: int, email: str, expiry_date: str):
    
    subject = "Your subscription is about to expire"
    body = f"""
    <html>
    <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; margin: 0; padding: 20px;">
        <div style="max-width: 600px; margin: 0 auto; padding: 30px; background-color: #ffffff; border-radius: 10px; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #2c3e50; margin-bottom: 5px;">Subscription Expiry Notice</h2>
                <div style="height: 3px; width: 80px; background: linear-gradient(to right, #3498db, #2ecc71); margin: 0 auto;"></div>
            </div>
            <p style="font-size: 16px; text-align: center; margin-bottom: 25px;">Your subscription will expire on <span style="font-weight: bold; color: #e74c3c;">{expiry_date}</span>.</p>
            <div style="background-color: #f8f9fa; border-left: 4px solid #3498db; padding: 15px; margin: 20px 0; border-radius: 4px;">
                <p style="margin: 0; font-size: 15px;">To ensure uninterrupted access to our services, please renew your subscription before the expiry date.</p>
            </div>
           
            <p style="font-size: 14px; text-align: center; margin-top: 30px; color: #7f8c8d;">
                Thank you for choosing our service!
            </p>
        </div>
    </body>
    </html>
    """
    
    try:
        msg = MIMEMultipart()
        msg['From'] = settings.SMTP_EMAIL
        msg['To'] = email
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'html'))
        
        # Connect to SMTP server and send
        with smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_EMAIL, settings.SMTP_PASSWORD)
            server.send_message(msg)
        
        return True
    except Exception as e:
        return False
