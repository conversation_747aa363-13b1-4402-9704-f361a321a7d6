from fastapi_mail import FastMail, ConnectionConfig, MessageSchema, MessageType
from app.core.config import settings

conf = ConnectionConfig(
    MAIL_USERNAME=settings.SMTP_EMAIL,
    MAIL_PASSWORD=settings.SMTP_PASSWORD,
    MAIL_FROM=settings.SMTP_EMAIL,
    MAIL_PORT=settings.SMTP_PORT,
    MAIL_SERVER=settings.SMTP_SERVER,
    MAIL_STARTTLS=True,  
    MAIL_SSL_TLS=False,  
    USE_CREDENTIALS=True
)

logo_url = f"{settings.BACKEND_URL}/static/applogo/prdlogo.png"

async def send_verification_email(email: str, token: str):    
    verification_url = f"{settings.FRONTEND_URL}/auth/verify-email?token={token}"
    body = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; border-radius: 8px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <img src="{logo_url}" alt="Logo" style="max-height: 100px;">
            </div>
            <h2 style="text-align: center; color: #4CAF50;">Welcome to Our Service!</h2>
            <p style="font-size: 16px; text-align: center;">Thank you for signing up. To complete your registration, please verify your email address by clicking the link below. Link will be expired in 1 hour.</p>
            <div style="text-align: center; margin-top: 20px;">
                <a href="{verification_url}" 
                   style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px; display: inline-block;">
                   Verify Your Email
                </a>
            </div>
            <p style="font-size: 14px; text-align: center; margin-top: 30px; color: #777;">
                If you did not sign up for this account, please ignore this email.
            </p>
            <div style="text-align: center; margin-top: 20px;">
                <p style="font-size: 12px; color: #999;">&copy; 2025, Our Service. All Rights Reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """
    message = MessageSchema(
        subject="Verify Your Email",
        recipients=[email],  
        body=body,
        subtype=MessageType.html  
    )
    fm = FastMail(conf)
    await fm.send_message(message)


async def send_password_reset_email(email: str, token: str):
    reset_link = f"{settings.FRONTEND_URL}?token={token}"
    body = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; border-radius: 8px;">
            <div style="text-align: center; margin-bottom: 50px;">
                <img src="{logo_url}" alt="Logo" style="max-height: 100px;">
            </div>
            <h2 style="text-align: center; color: #FF5722;">Password Reset Request</h2>
            <p style="font-size: 16px; text-align: center;">We received a request to reset your password. If you made this request, please click the link below to proceed. Link will be expired in 1 hour.</p>
            <div style="text-align: center; margin-top: 20px;">
                <a href="{reset_link}" 
                   style="background-color: #FF5722; color: white; padding: 12px 20px; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px; display: inline-block;">
                   Reset Your Password
                </a>
            </div>
            <p style="font-size: 14px; text-align: center; margin-top: 30px; color: #777;">
                If you did not request a password reset, please ignore this email.
            </p>
            <div style="text-align: center; margin-top: 20px;">
                <p style="font-size: 12px; color: #999;">&copy; 2025, Our Service. All Rights Reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """
    message = MessageSchema(
        subject="Password Reset Request",
        recipients=[email],  
        body=body,
        subtype=MessageType.html 
    )
    fm = FastMail(conf)
    await fm.send_message(message)


async def send_email(email: str, subject: str, body: str):    
    message = MessageSchema(
        subject=subject,
        recipients=[email],  
        body=body,
        subtype=MessageType.html  
    )
    fm = FastMail(conf)
    await fm.send_message(message)