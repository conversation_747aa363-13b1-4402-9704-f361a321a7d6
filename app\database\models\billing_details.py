from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from app.database.base import Base
from sqlalchemy.orm import relationship

class BillingDetails(Base):
    __tablename__ = "billing_details"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    country = Column(String, nullable=True)
    address_line1 = Column(String, nullable=True)
    address_line2 = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    zip = Column(String, nullable=True)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

    user = relationship("User", backref="user_billing_details")