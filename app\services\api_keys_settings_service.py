from sqlalchemy.ext.asyncio import AsyncSession
from app.database.models.api_keys_settings_model import APIKeysSettings
from app.schemas.api_keys_setting_schema import APIKeyCreate, APIKeyUpdate
from sqlalchemy.future import select
from typing import Optional, List
from app.database.enum import KeyEnum
from cryptography.fernet import Fernet
from app.core.config import settings
import json

fernet = Fernet(settings.FERNET_KEY)

class APIKEYSETTINGService():
    @staticmethod
    def decrypt_api_key(encrypted_json: str) -> dict:
        encrypted_dict = json.loads(encrypted_json)
        return {
            key: fernet.decrypt(value.encode()).decode()
            for key, value in encrypted_dict.items()
        }
    
    @staticmethod
    def encrypt_key_values(data: dict) -> dict:
        return {
            key: fernet.encrypt(value.encode()).decode()
            for key, value in data.items()
        }
    
    @staticmethod
    async def create_api_key(db: AsyncSession, api_key_data: APIKeyCreate) -> APIKeysSettings:
        existing_key = db.execute(select(APIKeysSettings).where(APIKeysSettings.key_name == api_key_data.key_name))
        if existing_key.scalars().first():
            raise ValueError("API Key with this name already exists.")
        encrypted_dict = APIKEYSETTINGService.encrypt_key_values(api_key_data.key_id)

        new_key = APIKeysSettings(
            key_name=api_key_data.key_name,
            key_id=encrypted_dict
        )
        db.add(new_key)
        db.commit()
        db.refresh(new_key)
        return new_key
    
    @staticmethod
    async def get_api_key(db: AsyncSession, key_id: int) -> Optional[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).where(APIKeysSettings.id == key_id))
        keys = result.scalars().first()
        if not keys:
            return ValueError("API Key not found !!")
        return keys
        
    

    @staticmethod
    async def get_all_api_keys(db: AsyncSession) -> List[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).order_by(APIKeysSettings.id))
        keys = result.scalars().all()
        if not keys:
            raise ValueError("API Keys not found !!")
        return keys


    @staticmethod
    async def update_api_key(db: AsyncSession, api_key_data: APIKeyUpdate) -> Optional[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).where(APIKeysSettings.id == api_key_data.id))
        api_key = result.scalars().first()
        if not api_key:
            return ValueError("API Key not found !!")
        encrypted_dict = APIKEYSETTINGService.encrypt_key_values(api_key_data.key_id)
        api_key.key_name = api_key_data.key_name
        api_key.key_id = encrypted_dict  
        db.commit()
        db.refresh(api_key)
        return api_key


    @staticmethod
    def get_api_key_by_name(db: AsyncSession, key_name: KeyEnum) -> Optional[APIKeysSettings]:
        result =  db.execute(select(APIKeysSettings).where(APIKeysSettings.key_name == key_name))
        return result.scalars().first() 

    