"""change in payment table

Revision ID: 8c2673657722
Revises: d8e94136906f
Create Date: 2025-04-29 15:33:43.606229

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8c2673657722'
down_revision: Union[str, None] = 'd8e94136906f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'auto_payment',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'auto_payment',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    # ### end Alembic commands ###
