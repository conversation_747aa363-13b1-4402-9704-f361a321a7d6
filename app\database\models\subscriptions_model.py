from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from app.database.base import Base
from sqlalchemy.sql import func

class Subscription(Base):
    __tablename__ = "subscriptions"
    subscription_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=False)
    payment_id = Column(Integer, ForeignKey("payments.payment_id"), nullable=False)
    uploads=Column(Integer,nullable=False)
    expiry_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)
    user = relationship("User", backref="user_subscriptions")
    plan = relationship("Plan", backref="plan_subscriptions")
    payment = relationship("Payment", backref="payment_subscriptions")

