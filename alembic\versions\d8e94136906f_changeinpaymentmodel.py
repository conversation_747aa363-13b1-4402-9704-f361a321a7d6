"""Changeinpaymentmodel

Revision ID: d8e94136906f
Revises: 6e61035b48c0
Create Date: 2025-04-29 14:56:57.228181

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd8e94136906f'
down_revision: Union[str, None] = '6e61035b48c0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payments', sa.Column('auto_payment', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payments', 'auto_payment')
    # ### end Alembic commands ###
