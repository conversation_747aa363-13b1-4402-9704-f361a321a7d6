from fastapi import APIRouter,Depends,HTTPException,Query
from app.schemas.api_keys_setting_schema import APIKeyCreate, APIKeyResponse,APIKeyUpdate
from app.database.session import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.api_keys_settings_service import APIKEYSETTINGService
from app.database.dependencies import role_required
from typing import List


router = APIRouter()

@router.post("/add_api_key", response_model=APIKeyResponse)
async def create_api_key(api_key: APIKeyCreate, db: AsyncSession = Depends(get_db), current_user: str = Depends(role_required(["Admin"]))):
    try:
        return await APIKEYSETTINGService.create_api_key(db, api_key)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
@router.get("/get_api_key_by_id", response_model=APIKeyResponse)
async def get_api_key(key_id: int, db: AsyncSession = Depends(get_db), current_user: str = Depends(role_required(["Admin"]))):
    api_key = await APIKEYSETTINGService.get_api_key(db, key_id)
    if not api_key:
        raise HTTPException(status_code=404, detail="API Key not found")
    return api_key


@router.get("/get_all_api_keys", response_model=List[APIKeyResponse])
async def get_all_api_keys(db: AsyncSession = Depends(get_db), current_user: str = Depends(role_required(["Admin"]))):
    return await APIKEYSETTINGService.get_all_api_keys(db)

@router.put("/update_api_keys", response_model=APIKeyResponse)
async def update_api_key( api_key: APIKeyUpdate, db: AsyncSession = Depends(get_db), current_user: str = Depends(role_required(["Admin"]))):
    updated_key = await APIKEYSETTINGService.update_api_key(db, api_key)
    if not updated_key:
        raise HTTPException(status_code=404, detail="API Key not found")
    return updated_key


