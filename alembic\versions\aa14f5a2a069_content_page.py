"""Content Page

Revision ID: aa14f5a2a069
Revises: 65acfe0b762a
Create Date: 2025-06-27 10:57:38.761790

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'aa14f5a2a069'
down_revision: Union[str, None] = '65acfe0b762a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('content_pages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('slug', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('content', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_content_pages_id'), 'content_pages', ['id'], unique=False)
    op.drop_index('ix_api_keys_id', table_name='api_keys')
    op.drop_index('ix_api_keys_key_name', table_name='api_keys')
    op.drop_table('api_keys')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_keys',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('key_name', postgresql.ENUM('STRIPE_KEY', 'GOOGLE_OAUTH_KEY', 'GITHUB_OAUTH_KEY', 'MICROSOFT_OAUTH_KEY', 'OPENAI_KEY', 'GUESTUSER_KEY', name='apikeynameenum'), autoincrement=False, nullable=False),
    sa.Column('key_id', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='api_keys_pkey')
    )
    op.create_index('ix_api_keys_key_name', 'api_keys', ['key_name'], unique=True)
    op.create_index('ix_api_keys_id', 'api_keys', ['id'], unique=False)
    op.drop_index(op.f('ix_content_pages_id'), table_name='content_pages')
    op.drop_table('content_pages')
    # ### end Alembic commands ###
