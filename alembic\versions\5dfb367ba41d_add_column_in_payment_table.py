"""Add column in payment table

Revision ID: 5dfb367ba41d
Revises: 99586db149c6
Create Date: 2025-04-28 11:30:13.638821

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5dfb367ba41d'
down_revision: Union[str, None] = '99586db149c6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payments', sa.Column('recipient_email', sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payments', 'recipient_email')
    # ### end Alembic commands ###
