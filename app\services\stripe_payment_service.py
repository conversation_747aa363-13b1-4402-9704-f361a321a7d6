import stripe
from app.core.config import settings
from sqlalchemy.orm import Session
from app.database.models.payment_model import Payment
from app.database.models.plan_model import Plan
from app.database.models.user_model import User
from app.database.models.user_uploads_model import UserUpload
from app.database.models.subscriptions_model import Subscription
from datetime import datetime
from fastapi import HTTPException, status, Request
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from datetime import datetime, timedelta
from app.tasks import send_subscription_reminder_email
from app.database.models.billing_details import BillingDetails
from app.tasks.utilis import generate_task_id
from celery.result import AsyncResult
from app.database.enum import KeyEnum
from app.services.api_keys_settings_service import APIKEYSETTINGService
 


class StripePaymentService:

    @staticmethod
    def create_payment_checkout_session(payment_data, current_user, db: Session):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.STRIPE_KEYS)
        stripe_secret_key = key.key_id["STRIPE_SECRET_KEY"]
        if stripe_secret_key:
            stripe.api_key = stripe_secret_key
        else:
            stripe.api_key = settings.STRIPE_SECRET_KEY

        if not current_user or not current_user.id:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not authenticated")

        try:
            plan_data = db.query(Plan).filter(Plan.id == payment_data.plan_id).first()
            if not plan_data or not plan_data.stripe_price_id:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Invalid or missing Stripe price ID for plan")

            if payment_data.mode not in ['payment', 'subscription']:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Invalid mode: Must be either 'payment' or 'subscription'")

            existing_price = stripe.Price.retrieve(plan_data.stripe_price_id)
            recurring_interval = existing_price.get("recurring", {})
            product_id = existing_price.product

            price_data = {
                "unit_amount": int(plan_data.price * 100),
                "currency": "usd",
                "product": product_id
            }
            if payment_data.mode == "subscription":
                if not plan_data.duration:
                    raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Subscription plans require a duration")
                price_data["recurring"] = {
                    "interval": recurring_interval.get("interval"),
                    "interval_count": recurring_interval.get("interval_count")
                }

            new_price = stripe.Price.create(**price_data)
            price_id = new_price.id

            user_billing_address = db.query(BillingDetails).filter_by(user_id=current_user.id).first()

            session_params = {
                "line_items": [{"price": price_id, "quantity": 1}],
                "mode": payment_data.mode,
                "success_url": f"{settings.FRONTEND_URL}/subscription/thankspage?session_id={{CHECKOUT_SESSION_ID}}",
                "cancel_url": f"{settings.FRONTEND_URL}/",
                "metadata": {"user_id": str(current_user.id), "plan_id": str(plan_data.id)},
                "customer_email": current_user.email,
                "client_reference_id": str(current_user.id),
                "payment_method_types": ["card", "cashapp"],
                "currency": "usd",
                 "billing_address_collection": "auto" if user_billing_address else "required"
            }

            if payment_data.mode == "payment":
                session_params["payment_intent_data"] = {
                    "description": f"One-time payment for Plan: {plan_data.plan_name}",
                    "metadata": {"plan_name": plan_data.plan_name, "user_email": current_user.email}
                }

            elif payment_data.mode == "subscription":
                session_params["subscription_data"] = {
                    "metadata": {"plan_name": plan_data.plan_name, "user_email": current_user.email}
                }

            checkout_session = stripe.checkout.Session.create(**session_params)

            payment = Payment(
                user_id=current_user.id,
                plan_id=plan_data.id,
                payment_intent_id=checkout_session.id,
                payment_method="unknown",
                amount=plan_data.price,
                currency="usd",
                recipient_email=current_user.email,
                payment_status="pending",
                auto_payment=False,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(payment)
            db.commit()
            db.refresh(payment)

            return {"checkout_session_id": checkout_session.id, "checkout_url": checkout_session.url}

        except stripe.error.StripeError as e:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Stripe error: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Internal server error: {str(e)}")


    @staticmethod
    async def stripe_webhook_service(request: Request, db: Session):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.STRIPE_KEYS)
        stripe_webhook_key = key.key_id["WEBHOOK_KEY"]
        if stripe_webhook_key:
            WEBHOOK_SECRET = stripe_webhook_key
        else:
            WEBHOOK_SECRET = settings.WEBHOOK_KEY          
        try:            
            payload = await request.body()
            sig_header = request.headers.get('stripe-signature')
            try:
                event = stripe.Webhook.construct_event(payload, sig_header, WEBHOOK_SECRET)
            except stripe.error.SignatureVerificationError:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Invalid Stripe signature")
            event_type = event["type"]           
            event_data = event["data"]["object"]         
            
            if event_type == "checkout.session.completed":                
                session = event["data"]["object"]
                session_id = session["id"]
                payment_intent_id = session.get("payment_intent")
                subscription_id = session.get("subscription")
                mode = session.get("mode")  
                
                existing_payment = db.query(Payment).filter(Payment.payment_intent_id == session_id).first()
                if not existing_payment:
                    return HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Payment not found")
                if existing_payment.payment_status == "succeeded":
                    return JSONResponse(content={"message": "Payment already succeeded"})
                
                payment_type = "unknown"
                final_payment_intent_id = session_id  
                is_auto_payment = False
                
                if payment_intent_id:
                    # One-time payment
                    payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                    payment_method_id = payment_intent.payment_method
                    if payment_method_id:
                        payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
                        payment_type = payment_method.type
                    final_payment_intent_id = payment_intent_id
                    is_auto_payment = False
                    
                elif subscription_id:
                    # Subscription payment                    
                    subscription = stripe.Subscription.retrieve(subscription_id)
                    if subscription.default_payment_method:
                        payment_method = stripe.PaymentMethod.retrieve(subscription.default_payment_method)
                        payment_type = payment_method.type
                    final_payment_intent_id = f"subscription/{subscription_id}"
                    is_auto_payment = True
                
                # Extract billing data from the event
                billing_details_data = event_data.get("customer_details", {})
                address = billing_details_data.get("address", {})
                # Fetch existing billing details
                existing_billing_details = db.query(BillingDetails).filter_by(user_id=existing_payment.user_id).first()

                if existing_billing_details:
                    # Update existing billing details
                    existing_billing_details.country = address.get("country")
                    existing_billing_details.address_line1 = address.get("line1")
                    existing_billing_details.address_line2 = address.get("line2")
                    existing_billing_details.city = address.get("city")
                    existing_billing_details.state = address.get("state")
                    existing_billing_details.zip = address.get("postal_code")
                    existing_billing_details.updated_at = datetime.now()
                else:
                    # Create new billing details
                    new_billing_details = BillingDetails(
                        user_id=existing_payment.user_id,
                        country=address.get("country"),
                        address_line1=address.get("line1"),
                        address_line2=address.get("line2"),
                        city=address.get("city"),
                        state=address.get("state"),
                        zip=address.get("postal_code"),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.add(new_billing_details)
                db.commit()
                # Update payment record
                if existing_payment:
                    existing_payment.payment_intent_id = final_payment_intent_id
                    existing_payment.payment_status = "succeeded"
                    existing_payment.payment_method = payment_type
                    existing_payment.auto_payment = is_auto_payment
                    existing_payment.updated_at = datetime.now()
                    db.commit()

                    existing_plan = db.query(Plan).filter(Plan.id == existing_payment.plan_id).first()
                    existing_subscription = db.query(Subscription).filter(Subscription.user_id == existing_payment.user_id).first()
                    existing_user_uploads = db.query(UserUpload).filter(UserUpload.user_id == existing_payment.user_id).first()

                    plan_duration = datetime.now() + timedelta(days=existing_plan.duration)                    
                   
                    reminder_time = plan_duration - timedelta(hours=24)
                    if reminder_time > datetime.now():
                        user = db.query(User).filter(User.id == existing_payment.user_id).first()
                        task_id = generate_task_id(existing_payment.user_id, str(plan_duration))
                        result = AsyncResult(task_id)
                
                        if result.state in ("PENDING", "RECEIVED", "STARTED"):
                            send_subscription_reminder_email.apply_async(
                                args=[existing_payment.user_id, user.email, str(plan_duration)],
                                eta=reminder_time,
                                task_id=task_id
                            )
                    if existing_subscription:                    
                        existing_subscription.uploads = existing_plan.uploads
                        existing_subscription.plan_id = existing_payment.plan_id
                        existing_subscription.expiry_date = plan_duration
                        existing_subscription.payment_id = existing_payment.payment_id
                        existing_subscription.updated_at = datetime.now()
                        existing_subscription.created_at = datetime.now()
                        if existing_user_uploads:
                            existing_user_uploads.remaining_uploads = existing_plan.uploads
                            existing_user_uploads.updated_at = datetime.now()
                            db.add(existing_user_uploads)
                        else:
                            new_upload = UserUpload(
                                user_id=existing_payment.user_id,
                                remaining_uploads=existing_plan.uploads,
                                created_at=datetime.now(),
                                updated_at=datetime.now()
                            )
                            db.add(new_upload)
                        db.commit()
                        return JSONResponse(content={"message": "Subscription updated successfully"})
                    else:
                        new_subscription = Subscription(
                            user_id=existing_payment.user_id,
                            plan_id=existing_payment.plan_id,
                            payment_id=existing_payment.payment_id,
                            uploads=existing_plan.uploads,
                            expiry_date=plan_duration,
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )
                        db.add(new_subscription)
                        if existing_user_uploads:
                            existing_user_uploads.remaining_uploads = existing_plan.uploads
                            existing_user_uploads.updated_at = datetime.now()
                            db.add(existing_user_uploads)
                        else:
                            new_upload = UserUpload(
                                user_id=existing_payment.user_id,
                                remaining_uploads=existing_plan.uploads,
                                created_at=datetime.now(),
                                updated_at=datetime.now()
                            )
                            db.add(new_upload)
                        db.commit()
                        return JSONResponse(content={"message": "Subscription created successfully"})

            elif event_type == "invoice.payment_succeeded":                        
                try:
                    invoice = event["data"]["object"]
                    payment_intent_id = invoice.get("payment_intent")
                    subscription_id = invoice.get("subscription")
                    billing_reason = invoice.get("billing_reason")

                    if billing_reason == "subscription_create":                        
                        return {"status": "success", "message": "Payment recorded successfully"}

                    else:                        
                        line_items = invoice.get("lines", {}).get("data", [])                       

                        if line_items:
                            parent = line_items[0].get("parent", {})                           

                            if parent.get("type") == "subscription_item_details":
                                subscription_details = parent.get("subscription_item_details", {})
                                subscription_id = subscription_details.get("subscription")                               

                                
                                existing_payment = db.query(Payment).filter_by(payment_intent_id=f"subscription/{subscription_id}").first()

                                if not existing_payment:
                                    raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Payment not found")
                                else:
                                    existing_user = db.query(User).filter_by(id=existing_payment.user_id).first()

                                payment_type = "unknown"
                                if payment_intent_id:
                                    payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                                    if payment_intent.payment_method:
                                        try:
                                            payment_method = stripe.PaymentMethod.retrieve(payment_intent.payment_method)
                                            payment_type = payment_method.type                                            
                                        except Exception as e:
                                            payment_type = str(e)                                            

                                new_payment = Payment(
                                    user_id=existing_user.id,
                                    plan_id=existing_payment.plan_id,
                                    payment_intent_id=f"subscription/{subscription_id}",  # Use subscription format
                                    payment_method=payment_type,
                                    recipient_email=existing_user.email,
                                    currency="usd",
                                    amount=existing_payment.amount,
                                    payment_status="succeeded",
                                    auto_payment=True,  # This is a recurring payment
                                    created_at=datetime.now(),
                                    updated_at=datetime.now()
                                )
                                db.add(new_payment)
                                db.commit()
                                db.refresh(new_payment)                               

                                existing_plan = db.query(Plan).filter(Plan.id == new_payment.plan_id).first()                               
                                existing_user_uploads = db.query(UserUpload).filter(UserUpload.user_id == new_payment.user_id).first()
                                existing_subscription = db.query(Subscription).filter(Subscription.user_id == new_payment.user_id).first()

                                plan_duration = datetime.now() + timedelta(days=existing_plan.duration)

                                if existing_subscription:
                                    existing_subscription.uploads = existing_plan.uploads
                                    existing_subscription.plan_id = new_payment.plan_id
                                    existing_subscription.expiry_date = plan_duration
                                    existing_subscription.payment_id = new_payment.payment_id
                                    existing_subscription.updated_at = datetime.now()

                                    if existing_user_uploads:
                                        existing_user_uploads.remaining_uploads += existing_plan.uploads
                                        existing_user_uploads.updated_at = datetime.now()
                                        db.add(existing_user_uploads)                                                                                                          
                                    db.commit()
                                   
                                    return JSONResponse(content={"message": "Subscription updated successfully"})
                except Exception as e:
                    raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Error processing invoice: {str(e)}")
                    
            elif event["type"] == "payment_intent.payment_failed":
                payment_intent = event["data"]["object"]
                session_id = payment_intent["id"]                    
                payment_intent_id = payment_intent["id"]
                payment_type = "unknown"
                
                if payment_intent_id:
                    payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                    payment_method_id = payment_intent.payment_method  
                    if payment_method_id:
                        payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
                        payment_type = payment_method.type  
                
                db_payment = db.query(Payment).filter_by(payment_intent_id=session_id).first()
                if db_payment:
                    db_payment.payment_status = "failed"
                    db_payment.payment_method = payment_type
                    db.commit()
                    
            elif event["type"] == "checkout.session.expired":
                session = event["data"]["object"]
                session_id = session["id"]
                db_payment = db.query(Payment).filter_by(payment_intent_id=session_id).first()
                if db_payment:
                    db_payment.payment_status = "expired"
                    db.commit()
                return {"status": "success"} 
            else:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Unhandled event type: {event_type}")               
            return JSONResponse(content={"message": "Webhook processed successfully"})
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Error processing Stripe webhook: {str(e)}")

    @staticmethod
    def cancel_subscription_autopayment_service(current_user, db: Session):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.STRIPE_KEYS)
        stripe_secret_key = key.key_id["STRIPE_SECRET_KEY"]
        if stripe_secret_key:
            stripe.api_key = stripe_secret_key
        else:
            stripe.api_key = settings.STRIPE_SECRET_KEY 
        try:
            subscription = db.query(Subscription).filter_by(user_id=current_user.id).first()
            if not subscription:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Subscription not found")
            existing_payment = db.query(Payment).filter_by(payment_id=subscription.payment_id,auto_payment = True).first()
            if not existing_payment:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Payment not found or Auto payment is not enabled")
            
            # Extract subscription ID from the payment_intent_id
            if existing_payment.payment_intent_id.startswith("subscription/"):
                stripe_subscription_id = existing_payment.payment_intent_id.replace("subscription/", "")
            else:
                stripe_subscription_id = existing_payment.payment_intent_id
                
            sub_existing=stripe.Subscription.retrieve(stripe_subscription_id)
            if sub_existing.status in ["canceled", "incomplete_expired"]:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Subscription is already canceled or expired.")
            cancel_autopayment=stripe.Subscription.modify(stripe_subscription_id,cancel_at_period_end=True)
            if cancel_autopayment:
                existing_payment.auto_payment = False
                db.commit()
                return {"message": "Subscription canceled successfully"}
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail=f"Error processing Stripe webhook: {str(e)}")


    @staticmethod
    def get_payment_details_service(session_id: str, current_user, db: Session):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.STRIPE_KEYS)
        stripe_secret_key = key.key_id["STRIPE_SECRET_KEY"]
        if stripe_secret_key:
            stripe.api_key = stripe_secret_key
        else:
            stripe.api_key = settings.STRIPE_SECRET_KEY
        try:
            session = stripe.checkout.Session.retrieve(session_id)
            if session.payment_status == "paid":
                if session.mode == "payment":
                    return {
                    "payment_intent_id": session.payment_intent,
                    "amount": session.amount_total / 100,  
                    "status": session.status,
                    "mode": session.mode,
                }


                elif session.mode == "subscription":
                    return {
                    "subscription_id": session.subscription,
                    "amount": session.amount_total / 100, 
                    "status": session.status,
                    "mode": session.mode,
                }                
                
            else:
                raise HTTPException(
                    status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                    detail="Payment not completed"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail=f"Error retrieving payment session: {str(e)}"
            )
