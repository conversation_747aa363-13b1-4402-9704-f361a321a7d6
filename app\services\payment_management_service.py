from sqlalchemy.orm import Session
from app.database.models.payment_model import Payment
from app.database.models.user_model import User
from app.database.models.plan_model import Plan
from app.database.models.subscriptions_model import Subscription
from datetime import timedelta, datetime
from fastapi import HTTPException,status

class PaymentManagementService:
    @staticmethod
    def get_all_payments_service(db: Session, page: int, page_size: int, search: str, start_date: str, end_date: str, sort_by: str, sort_order: str, current_user=None):
        query = db.query(
            Payment,
            User.name.label("user_name"),
            User.email.label("user_email"),
            Plan.plan_name.label("plan_name")
        ).join(User, User.id == Payment.user_id).join(Plan, Plan.id == Payment.plan_id)
        
        # Filter by user_id if not Admin
        if current_user and current_user.role.name != "Admin":
            query = query.filter(Payment.user_id == current_user.id)
            
        if search:
            query = query.filter((User.name.ilike(f"%{search}%")) | (User.email.ilike(f"%{search}%")))            
        if start_date and end_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            end_date_obj = end_date_obj + timedelta(days=1)
            query = query.filter(Payment.created_at.between(start_date_obj, end_date_obj))
        elif start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(Payment.created_at >= start_date_obj)
        elif end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            end_date_obj = end_date_obj + timedelta(days=1)
            query = query.filter(Payment.created_at <= end_date_obj)
            
        SORT_MAPPING = {
            "user_name": User.name,  
            "user_email": User.email,
            "payment_id": Payment.payment_id,
            "amount": Payment.amount,
            "payment_status": Payment.payment_status,
            "created_at": Payment.created_at,
            "plan_name": Plan.plan_name,  
        }

        if sort_by:
            sort_column = SORT_MAPPING.get(sort_by)
            if sort_column is not None:
                query = query.order_by(sort_column.asc() if sort_order == "asc" else sort_column.desc())
            else:
                query = query.order_by(Payment.created_at.desc())
        else:
            query = query.order_by(Payment.created_at.desc())
            
        total = query.count()
        total_pages = (total + page_size - 1) // page_size
        payment_results = query.offset((page - 1) * page_size).limit(page_size).all()
        
 
        payments = []
        for result in payment_results:
            payment = result[0]       
            user_name = result[1]    
            user_email = result[2]   
            plan_name = result[3]                 
            
            payment_dict = {
                "payment_id": payment.payment_id,
                "user_id": payment.user_id,
                "plan_id": payment.plan_id,
                "payment_intent_id": payment.payment_intent_id,
                "payment_method": payment.payment_method,
                "recipient_email": payment.recipient_email,
                "currency": payment.currency,
                "amount": payment.amount,
                "auto_payment": payment.auto_payment,
                "payment_status": payment.payment_status,
                "created_at": payment.created_at,
                "updated_at": payment.updated_at,               
                "user_name": user_name,
                "user_email": user_email,
                "plan_name": plan_name
            }
            payments.append(payment_dict)
        
        return {
            "total": total,
            "total_pages": total_pages,
            "items": payments
        }
    
    @staticmethod
    def get_all_subscriptions_service(db: Session, page: int, page_size: int, search: str, start_date: str, end_date: str, sort_by: str, sort_order: str):        
        query = db.query(
            Subscription,
            User.name.label("user_name"),
            User.email.label("user_email"),
            Plan.plan_name.label("plan_name")
        ).join(User, User.id == Subscription.user_id).join(Plan, Plan.id == Subscription.plan_id)
        
        if search:
            query = query.filter((User.name.ilike(f"%{search}%")) | (User.email.ilike(f"%{search}%")))        
     
        if start_date and end_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")         
            end_date_obj = end_date_obj + timedelta(days=1)
            query = query.filter(Subscription.created_at.between(start_date_obj, end_date_obj))
        elif start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(Subscription.created_at >= start_date_obj)
        elif end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")            
            end_date_obj = end_date_obj + timedelta(days=1)
            query = query.filter(Subscription.created_at <= end_date_obj)
        
        SORT_MAPPING = {
            "user_name": User.name,  
            "user_email": User.email,
            "subscription_id": Subscription.subscription_id,
            "expiry_date": Subscription.expiry_date,
            "created_at": Subscription.created_at,            
            "plan_name": Plan.plan_name,  
        }
        if sort_by:
            sort_column = SORT_MAPPING.get(sort_by)
            if sort_column is not None:
                query = query.order_by(sort_column.asc() if sort_order == "asc" else sort_column.desc())
            else:
                query = query.order_by(Subscription.created_at.desc())
        else:
            query = query.order_by(Subscription.created_at.desc())
        
        total = query.count()
        total_pages = (total + page_size - 1) // page_size
        subscription_results = query.offset((page - 1) * page_size).limit(page_size).all()
      
        subscriptions = []
        for result in subscription_results:
            subscription = result[0]  
            user_name = result[1]     
            user_email = result[2]    
            plan_name = result[3]     
            
           
            subscription_dict = {
                "subscription_id": subscription.subscription_id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "expiry_date": subscription.expiry_date,
                "created_at": subscription.created_at,
                "updated_at": subscription.updated_at,                
                "user_name": user_name,
                "user_email": user_email,
                "plan_name": plan_name
            }
            subscriptions.append(subscription_dict)
        
        return {
            "total": total,
            "total_pages": total_pages,
            "items": subscriptions
        }

    @staticmethod
    def get_user_subscription_service(current_user,db:Session):
        subscription = db.query(Subscription).filter(Subscription.user_id == current_user.id).first()
        if not subscription:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Subscription not found")       
       
        is_active = subscription.expiry_date > datetime.now()       
       
        subscription_dict = {
            "subscription_id": subscription.subscription_id,
            "plan_name": subscription.plan.plan_name,  
            "user_name": subscription.user.name,
            "user_email": subscription.user.email,          
            "user_id": subscription.user_id,
            "plan_id": subscription.plan_id,
            "expiry_date": subscription.expiry_date,
            "created_at": subscription.created_at,
            "updated_at": subscription.updated_at,
            "is_active": is_active
        }
        return subscription_dict
