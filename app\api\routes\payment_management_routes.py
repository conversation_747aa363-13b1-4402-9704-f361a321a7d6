from fastapi import APIRouter, Depends, Query
from app.database.session import get_db
from app.database.dependencies import get_current_user, role_required
from app.services.payment_management_service import PaymentManagementService
from sqlalchemy.orm import Session

router = APIRouter()

@router.get("/get_all_payments")
def get_all_payments(
    current_user=Depends(get_current_user),
    db=Depends(get_db),
    page: int = Query(1, description="Page number (starts from 1)"),
    page_size: int = Query(10, description="Number of records per page"),
    search: str = Query(None, description="Search by name or email"),    
    start_date: str = Query(None, description="Filter users created after this date YYYY-MM-DD"),
    end_date: str = Query(None, description="Filter users created before this date YYYY-MM-DD"),
    sort_by: str = Query("user_name", description="Sort by all column "),
    sort_order: str = Query("desc", description="Sort order (asc/desc)")
):
    return PaymentManagementService.get_all_payments_service(
        db, page, page_size, search, start_date, end_date, sort_by, sort_order, current_user
    )

@router.get("/get_all_subscriptions")
def get_all_subscriptions(current_user=Depends(role_required(["Admin"])),
            db=Depends(get_db),
            page: int = Query(1, description="Page number (starts from 1)"),
            page_size: int = Query(10, description="Number of records per page"),
            search: str = Query(None, description="Search by name or email"),
            start_date: str = Query(None, description="Filter users created after this date YYYY-MM-DD"),
            end_date: str = Query(None, description="Filter users created before this date YYYY-MM-DD"),
            sort_by: str = Query("user_name", description="Sort by  all column "),
            sort_order: str = Query("desc", description="Sort order (asc/desc)")
            ):
    return PaymentManagementService.get_all_subscriptions_service(db, page, page_size, search, start_date, end_date, sort_by, sort_order)


@router.get("/get_user_subscription")
def get_user_subscription(current_user=Depends(get_current_user),db:Session = Depends(get_db)):
    return PaymentManagementService.get_user_subscription_service(current_user,db)

