from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from app.database.base import Base
from sqlalchemy.orm import relationship


class UserSession(Base):
    __tablename__ = "user_sessions"
    id = Column(Integer, primary_key=True, index=True)
    jti_id = Column(String, nullable=True,unique=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)
    user = relationship("User", backref="user_sessions")