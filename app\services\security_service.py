from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.database.models.security_incident_model import SecurityIncident
from app.database.models.user_model import User
from app.database.models.audit_logs_model import AuditLog
from app.database.models.user_session_model import UserSession
from app.database.models.user_model import RevokedToken

class SECURITY_MONITOR_SERVICE:

    @staticmethod
    def get_security_alerts(
        db: Session, page: int, page_size: int, search: str = None
    ):
        query = db.query(SecurityIncident)

        if search:
            query = query.filter(
                or_(
                    SecurityIncident.description.ilike(f"%{search}%"),
                    SecurityIncident.severity.ilike(f"%{search}%"),
                    SecurityIncident.source.ilike(f"%{search}%")
                )
            )

        total_items = query.count()
        offset = (page - 1) * page_size
        incidents = query.order_by(SecurityIncident.timestamp.desc()).offset(offset).limit(page_size).all()

        admin_ids = list({incident.admin_id for incident in incidents})
        admins = db.query(User.id, User.name).filter(User.id.in_(admin_ids)).all()
        admin_lookup = {admin.id: admin.name for admin in admins}

        return total_items, incidents, admin_lookup


    @staticmethod
    def get_audit_logs(
        db: Session, page: int, page_size: int, search: str = None
    ):
        query = db.query(AuditLog)

        if search:
            query = query.filter(
                or_(
                    AuditLog.action.ilike(f"%{search}%"),
                    AuditLog.ip_address.ilike(f"%{search}%"),
                    AuditLog.admin_id.ilike(f"%{search}%")
                )
            )

        total_items = query.count()
        offset = (page - 1) * page_size
        logs = query.order_by(AuditLog.timestamp.desc()).offset(offset).limit(page_size).all()

        admin_ids = list({log.admin_id for log in logs})
        admins = db.query(User.id, User.name).filter(User.id.in_(admin_ids)).all()
        admin_lookup = {admin.id: admin.name for admin in admins}

        return total_items, logs, admin_lookup
    


    @staticmethod
    def terminate_user_session_service(user_id,db):
        user_sessions = db.query(UserSession).filter(UserSession.user_id == user_id).all()
        for session in user_sessions:
            revoken_token = RevokedToken(jti=session.jti_id)
            db.add(revoken_token)
            db.delete(session)
        db.commit()
        return {"message":"User session terminated successfully!"}