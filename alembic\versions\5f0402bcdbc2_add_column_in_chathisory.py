"""add column in chathisory

Revision ID: 5f0402bcdbc2
Revises: 24103f854ddc
Create Date: 2025-05-29 14:59:42.004644

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5f0402bcdbc2'
down_revision: Union[str, None] = '24103f854ddc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_history', sa.Column('chat_id', sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_history', 'chat_id')
    # ### end Alembic commands ###
