from app.core.redis import redis_client

class CacheService:
    @staticmethod
    def invalidate_chat_cache(user_id: int):
        pattern = f"chat_history_user_{user_id}_*"
        for key in redis_client.scan_iter(pattern):
            redis_client.delete(key)

    @staticmethod
    def invalidate_chat_data_cache(user_id: int, session_id: str):
        key = f"chat_data_user_{user_id}_chat_{session_id}"
        redis_client.delete(key)
