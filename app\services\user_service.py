from fastapi import HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from app.database.models.user_model import User
from app.core.password import get_password_hash,verify_password
from app.core.security import generate_verification_token,verify_token,create_access_token,create_refresh_token,is_token_revoked,revoke_token,verify_access_token,token_jti
from app.services.email_service import send_verification_email,send_password_reset_email
from app.schemas.user_schema import UserCreate,UserLogin,ForgotPassword,ResetPassword,UpdatePassword
from app.database.enum import RoleEnum
from datetime import datetime
from app.database.models.user_uploads_model import UserUpload
from app.database.models.plan_model import Plan
from app.database.models.user_session_model import UserSession



def authenticate_user(db: Session, email: str, password: str,role:str):
    user = db.query(User).filter(User.email == email,User.role == role).first()
    if not user or not verify_password(password, user.password):
        return None
    return user
class Authentication:

    @staticmethod
    def register_user_service(user_data: UserCreate, background_tasks: BackgroundTasks, db: Session):
            if user_data.password != user_data.confirmpassword:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Passwords do not match")    
            if not user_data.email or user_data.email == "":
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Email is required")
            if not user_data.name or user_data.name == "":
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Name is required")
            if not user_data.password or user_data.password == "":
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Password is required")
            if not user_data.confirmpassword or user_data.confirmpassword == "":
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Confirm password is required")
            if db.query(User).filter(User.email == user_data.email).first():
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")    
            hashed_password = get_password_hash(user_data.password)      
            new_user = User(
                name=user_data.name,
                email=user_data.email.lower(),
                password=hashed_password,
                role=user_data.role if user_data.role else RoleEnum.User,
                is_active=False,
                platform="web",
                is_deleted=False
            )    
            db.add(new_user)
            db.commit()
            db.refresh(new_user)    
            token = generate_verification_token(new_user.email)
            background_tasks.add_task(send_verification_email, new_user.email, token)  
            free_plan = db.query(Plan).filter(Plan.price == 0).first()
            if free_plan:
                user_uploads = UserUpload(user_id=new_user.id, remaining_uploads=free_plan.uploads, created_at=datetime.now(), updated_at=datetime.now())
                db.add(user_uploads)
                db.commit()           
            else:
                pass  
            return {"message": "User registered successfully. Please check your email to verify your account."}

    @staticmethod
    def verify_email_service(token: str, db: Session):
        email = verify_token(token)
        if not email:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Invalid or expired token")
        user = db.query(User).filter(User.email == email).first()
        if user.is_confirmed ==True:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Email is already verified")
        if not user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")
        if user.is_active:
            return {"message": "Email is already verified"}
        user.is_active = True
        db.commit()
        return {"message": "Email successfully verified!"}


    @staticmethod
    def login_user_service(user_data: UserLogin, db: Session):
        if not user_data.email:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Email is required")
        if not user_data.password:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Password is required")
        user = authenticate_user(db, user_data.email.lower(), user_data.password,user_data.role)       
        if not user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
        elif user.is_active==False:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Your account is Inactive.Please contact with admin to activate your account.")             
        access_token = create_access_token({"sub": user.email,"role":str(user.role.value),"id":user.id})
        refresh_token = create_refresh_token({"sub": user.email,"role":str(user.role.value),"id":user.id})
        if access_token:
             jti = token_jti(access_token)
             user_session = UserSession(user_id=user.id, jti_id=jti, created_at=datetime.now(), updated_at=datetime.now())
             db.add(user_session)
             db.commit()            
        if access_token and refresh_token:
            user.last_login=datetime.now()
            db.commit()
        return {"access_token": access_token, "refresh_token": refresh_token, "token_type": "bearer", "role":user.role,"message": "Successfully logged in"}
    
    @staticmethod
    def logout_user_service(current_user,db: Session):
        token = current_user.token
        if not token:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Token not found")
        if is_token_revoked(token, db):
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Token already revoked")
        revoke_token(token, db)
        return {"message": "Successfully logged out"}
    
    @staticmethod
    def forgot_password_service(request: ForgotPassword,background_tasks, db: Session):
        if not request.email:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Email is required")
        user = db.query(User).filter(User.email == request.email).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")
        token = generate_verification_token(user.email)
        background_tasks.add_task(send_password_reset_email, user.email, token)
        return {"message": "Password reset link sent to your email"}
    
    @staticmethod
    def reset_password_service(request:ResetPassword, db: Session):
        email = verify_token(request.token)
        if not email:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Invalid or expired token")
        user = db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")
        if request.password != request.confirmpassword:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Passwords do not match")
        user.password = get_password_hash(request.password)
        db.commit()
        return {"message": "Password reset successful"} 
    
    @staticmethod
    def update_password_service(request:UpdatePassword,current_user, db: Session):
        if verify_password(request.old_password,current_user.password):
            if verify_password(request.new_password, current_user.password):
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="New password must be different from old password")
            if request.new_password != request.confirm_password:
                raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Pasword and Confirm password not match")
            current_user.password = get_password_hash(request.new_password)
            db.commit()
            return {"message":"Password Updated"}
        raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,detail="Old password is incorrect")
    
    @staticmethod
    def get_new_access_token_service(refresh_token: str, db: Session):
        try:
            payload = verify_access_token(refresh_token)
            email: str = payload.get("sub")
            if email is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
            user = db.query(User).filter(User.email == email).first()
            if user is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
            access_token = create_access_token({"sub": user.email,"role":str(user.role.value),"id":user.id})
            refresh_token = create_refresh_token({"sub": user.email,"role":str(user.role.value),"id":user.id})
            return {"access_token": access_token,"refresh_token":refresh_token, "token_type": "bearer"}
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
