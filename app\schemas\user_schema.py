from pydantic import BaseModel,EmailStr,Field
from typing import Optional
from datetime import datetime


class UserBase(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
class UserCreate(UserBase):
    password: Optional[str] = None
    confirmpassword: Optional[str] = None
    role: Optional[str] = None
    token: Optional[str] = None

class UserLogin(BaseModel):
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role: Optional[str] = None

class ForgotPassword(BaseModel):
    email: Optional[EmailStr] = None
    
class ResetPassword(BaseModel):
    token:str
    password: Optional[str] = None
    confirmpassword: Optional[str] = None

class UpdatePassword(BaseModel):
    old_password: str
    new_password: str
    confirm_password: str

class UserResponse(UserBase):
    id:int
    last_login: datetime
    created_at: datetime
    profile_picture : Optional[str] = None  
    plan_name: Optional[str] = None

    class Config:
        from_attributes = True  
        json_encoders = {
            datetime: lambda v: v.isoformat()  
        } 

class UpdateResponse(BaseModel):
    id:int
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    class Config:
        from_attributes = True

class UpdateStatus(BaseModel):
    id:int
    is_active:bool
    class Config:
        from_attributes = True

