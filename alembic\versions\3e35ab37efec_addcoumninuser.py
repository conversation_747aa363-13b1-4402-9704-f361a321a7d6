"""AddCoumninuser

Revision ID: 3e35ab37efec
Revises: daaee9bed004
Create Date: 2025-04-30 16:41:47.283646

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3e35ab37efec'
down_revision: Union[str, None] = 'daaee9bed004'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.<PERSON>umn('is_confirmed', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'is_confirmed')
    # ### end Alembic commands ###
