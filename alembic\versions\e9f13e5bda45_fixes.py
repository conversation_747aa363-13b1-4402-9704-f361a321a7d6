"""fixes

Revision ID: e9f13e5bda45
Revises: 2f519e4fa1b0
Create Date: 2025-07-02 15:13:15.141274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e9f13e5bda45'
down_revision: Union[str, None] = '2f519e4fa1b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('atlassian_tokens', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'access_token',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'refresh_token',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'cloud_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('atlassian_tokens', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'cloud_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'refresh_token',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'access_token',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
