import enum
from sqlalchemy.dialects.postgresql import ENUM 

class RoleEnum(enum.Enum):
    Admin = "Admin"
    User = "User"
    
class KeyEnum(str,enum.Enum):
    STRIPE_KEYS = "STRIPE_KEYS"
    OPENAI_API_KEYS = "OPENAI_API_KEYS"
    GOOGLE_OAUTH_KEYS = "GOOGLE_OAUTH_KEYS"
    GITHUB_OAUTH_KEYS = "GITHUB_OAUTH_KEYS"
    NOTION_API_KEYS = "NOTION_API_KEYS"
    ATLASSIAN_API_KEYS = "ATLASSIAN_API_KEYS"
    SMTP_KEYS = "SMTP_KEYS"
    GUEST_USER_LIMIT = "GUEST_USER_LIMIT"
