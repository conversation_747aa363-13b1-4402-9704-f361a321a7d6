from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from app.database.session import get_db
from app.services.security_service import SECURITY_MONITOR_SERVICE
from app.database.dependencies import role_required

router = APIRouter()


@router.get("/security-incidents")
def security_incidents(db: Session = Depends(get_db),current_user=Depends(role_required(["Admin"])),page: int = Query(1, ge=1),page_size: int = Query(10, ge=1),search: str = Query(None)):
    total_items, incidents, admin_lookup = SECURITY_MONITOR_SERVICE.get_security_alerts(db, page, page_size, search)
    
    return {
        "page": page,
        "page_size": page_size,
        "total_items": total_items,
        "incidents": [
            {
                "id": incident.id,
                "timestamp": incident.timestamp,
                "admin_id": incident.admin_id,
                "reason": incident.reason,
                "action": incident.action,
                "admin_name": admin_lookup.get(incident.admin_id, "Unknown")
            }
            for incident in incidents
        ]
    }

@router.get("/audit-logs")
def audit_logs(db: Session = Depends(get_db),current_user=Depends(role_required(["Admin"])),page: int = Query(1, ge=1),page_size: int = Query(10, ge=1),search: str = Query(None)):
    total_items, logs, admin_lookup = SECURITY_MONITOR_SERVICE.get_audit_logs(db, page, page_size, search)
    
    return {
        "page": page,
        "page_size": page_size,
        "total_items": total_items,
        "logs": [
            {
                "admin_name": admin_lookup.get(log.admin_id, "Unknown"),
                "action": log.action,
                "ip": log.ip_address,
                "status_code": log.status_code,
                "user_agent": log.user_agent,
                "timestamp": log.timestamp
            }
            for log in logs
        ]
    }


@router.get("/terminate_user_session")
def terminate_user_session(user_id: int, db: Session = Depends(get_db),current_user=Depends(role_required(["Admin"]))):
    return SECURITY_MONITOR_SERVICE.terminate_user_session_service(user_id, db)