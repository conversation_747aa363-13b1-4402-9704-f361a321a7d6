1.Verify if the alembic.ini file exists in the root of your project. If it’s missing:

You can generate it by running:

alembic init alembic

Step 2: Configure the alembic.ini File
[alembic]
script_location = alembic
sqlalchemy.url = sqlite:///./test.db


Step 3: Modify env.py


Step 4: Run Alembic Commands


Initialize Alembic if not already done:
alembic init alembic

Generate a migration file:
alembic revision --autogenerate -m "Initial migration"

Apply migrations to the database:
alembic upgrade head

uvicorn app.main:app --reload



