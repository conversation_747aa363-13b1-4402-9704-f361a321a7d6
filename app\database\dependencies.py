from fastapi import Depends, HTTPException, status,Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import J<PERSON><PERSON>rror
from sqlalchemy.orm import Session
from app.core.security import verify_access_token, is_token_revoked
from app.database.session import get_db
from app.database.models.user_model import User
from typing import Optional

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    token = credentials.credentials  
    payload = verify_access_token(token)
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        if is_token_revoked(token, db):
            raise HTTPException(status_code=401, detail="Token has been revoked")

        payload = verify_access_token(token)
        if not payload:
            raise credentials_exception

        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception

    except J<PERSON><PERSON>rror:
        raise credentials_exception
    except Exception as e:        
        raise HTTPException(status_code=498, detail="Token invalid/expired !")

    user = db.query(User).filter(User.email == email).first()
    if user is None or not user.is_active:
        raise HTTPException(status_code=403, detail="Inactive or non-existent user")
    user.token = token    
    return user

def role_required(role: list):
    def role_dependency(current_user: User = Depends(get_current_user)):
        if not current_user:
            raise HTTPException(
                status_code=401, detail="Authentication required"
            )
        if current_user.role.name not in role:
            raise HTTPException(status_code=403, detail="Access forbidden")
        
        return current_user
    return role_dependency

async def optional_get_current_user(request: Request,db: Session = Depends(get_db)) -> Optional[User]:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return None
    try:
        token = auth_header.split(" ")[1]
    except IndexError:
        return None
    try:
        if is_token_revoked(token, db):
            return None
        payload = verify_access_token(token)
        if not payload:
            return None
        email = payload.get("sub")
        if not email:
            return None
        user = db.query(User).filter(User.email == email).first()
        if user is None or not user.is_active:
            return None
        user.token = token
        return user
    except JWTError:
        return None
    except Exception:
        return None