"""add raw token

Revision ID: c52741e5c6f2
Revises: 24831ba7693a
Create Date: 2025-06-30 10:53:24.935322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c52741e5c6f2'
down_revision: Union[str, None] = '24831ba7693a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notion_tokens', sa.Column('raw_token_data', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('notion_tokens', 'raw_token_data')
    # ### end Alembic commands ###
