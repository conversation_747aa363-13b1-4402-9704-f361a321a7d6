from sqlalchemy import Column, Integer, String, DateTime, Boolean,ARRAY
from app.database.base import Base

class Plan(Base):
    __tablename__ = "plans"
    id = Column(Integer, primary_key=True, index=True)
    plan_name=Column(String,nullable=False)
    description=Column(String,nullable=False)
    uploads=Column(Integer,nullable=False)
    features=Column(ARRAY(String),nullable=False)
    price=Column(Integer,nullable=False)
    duration=Column(Integer,nullable=False)
    plan_status=Column(Boolean,nullable=False)
    stripe_price_id=Column(String,nullable=False)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

