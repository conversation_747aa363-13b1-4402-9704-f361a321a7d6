import httpx
import urllib.parse
from app.core.config import settings
import base64
import json
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.database.models.atlassian_tokens_model import AtlassianToken
from fastapi import HTTPException

class ConfluenceOAuthService:

    @staticmethod
    def get_authorization_url(current_user):
        
        user_id = current_user.id
        user_data = {"user_id": user_id}
        encoded_state = base64.b64encode(json.dumps(user_data).encode()).decode()
        base_url = "https://auth.atlassian.com/authorize"
        params = {
            "audience": "api.atlassian.com",
            "client_id": settings.ATLASSIAN_CLIENT_ID,
            "scope": "read:confluence-content.all write:confluence-content read:confluence-space.summary read:space:confluence write:space:confluence read:confluence-user offline_access",
            "redirect_uri": settings.ATLASSIAN_REDIRECT_URI,
            "response_type": "code",
            "prompt": "consent",
            "state": encoded_state
        }
        return f"{base_url}?{urllib.parse.urlencode(params)}"

    @staticmethod
    async def exchange_code_for_token(code: str):
        url = "https://auth.atlassian.com/oauth/token"
        payload = {
            "grant_type": "authorization_code",
            "client_id": settings.ATLASSIAN_CLIENT_ID,
            "client_secret": settings.ATLASSIAN_CLIENT_SECRET,
            "code": code,
            "redirect_uri": settings.ATLASSIAN_REDIRECT_URI
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            return response.json()

    @staticmethod
    async def refresh_access_token(refresh_token: str):
        """Refresh access token using refresh token"""
        url = "https://auth.atlassian.com/oauth/token"
        payload = {
            "grant_type": "refresh_token",
            "client_id": settings.ATLASSIAN_CLIENT_ID,
            "client_secret": settings.ATLASSIAN_CLIENT_SECRET,
            "refresh_token": refresh_token
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            return response.json()

    @staticmethod
    async def get_valid_access_token(user_id: int, db: Session):
        """Get a valid access token, refreshing if necessary"""
        token_record = db.query(AtlassianToken).filter_by(user_id=user_id).first()
        if not token_record:
            raise ValueError("No Atlassian token found for user. Please re-authorize.")
        if datetime.utcnow() >= (token_record.expires_at - timedelta(minutes=5)):            
            try:
                token_data = await ConfluenceOAuthService.refresh_access_token(token_record.refresh_token)                
                token_record.access_token = token_data["access_token"]
                token_record.expires_at = datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                if "refresh_token" in token_data:
                    token_record.refresh_token = token_data["refresh_token"]                
                db.commit()                
            except Exception as e:               
                raise ValueError("Failed to refresh access token. Please re-authorize.")
        
        return token_record.access_token

    @staticmethod
    async def get_cloud_id(access_token: str):
        url = "https://api.atlassian.com/oauth/token/accessible-resources"
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            resources = response.json()
            
            if not resources:
                raise ValueError("No accessible Atlassian resources found")
            
            return resources[0]["id"]
        
    @staticmethod
    async def get_confluence_spaces(user_id: int, db: Session):
        """Get all accessible Confluence spaces using v2 API with proper scope handling"""
        access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
        async with httpx.AsyncClient() as client:
            resources_resp = await client.get(
                "https://api.atlassian.com/oauth/token/accessible-resources",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            resources_resp.raise_for_status()
            resources = resources_resp.json()
            all_spaces = []            
            for resource in resources:
                scopes = resource.get("scopes", [])            
                required_space_scope = "read:confluence-space.summary"
                has_confluence_access = required_space_scope in scopes
                if not has_confluence_access:
                    continue
                cloud_id = resource["id"]                
                endpoints_to_try = [
                    f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/spaces",
                    f"https://api.atlassian.com/ex/confluence/{cloud_id}/api/v2/spaces",
                    f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/rest/api/space"
                ]                
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Accept": "application/json"
                }                
                for i, endpoint in enumerate(endpoints_to_try):
                    if endpoint is None:
                        continue                        
                    try:
                        if "v2" in endpoint:
                            test_url = f"{endpoint}?limit=25"
                        else:
                            test_url = f"{endpoint}?limit=25&expand=description,homepage"
                        
                        resp = await client.get(test_url, headers=headers)
                        resp.raise_for_status()
                        
                        data = resp.json()
                        
                        if "v2" in endpoint:
                            spaces = data.get("results", [])
                        else:
                            spaces = data.get("results", [])
                        
                        all_spaces.extend(spaces)
                        break                         
                    except Exception as e:
                        print(f"Unexpected error with endpoint {endpoint}: {str(e)}")
                        continue
            if not all_spaces:
                raise ValueError(
                    "No Confluence spaces found. This could be due to:\n"
                    "1. Insufficient OAuth scopes (need read:confluence-space.summary or similar)\n"
                    "2. User doesn't have access to any Confluence spaces\n"
                    "3. Confluence instance is not accessible\n"
                    "Check your OAuth app configuration and user permissions."
                )            
            return {"results": all_spaces}


    @staticmethod
    async def create_confluence_page_with_debug(user_id: int, db: Session, cloud_id: str, space_id: str, title: str, content: str):
        """Create page with detailed debugging"""
        
        access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
        breakpoint()
        # 🔍 DEBUG: Check what scopes we actually have
        await ConfluenceOAuthService.debug_current_token_scopes(access_token)
    
        
        url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/pages"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        payload = {
            "spaceId": space_id,
            "status": "current",
            "title": title,
            "body": {
                "representation": "storage", 
                "value": content
            }
        }
        
        print(f"🚀 Attempting to create page...")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            
            print(f"📊 Response Status: {response.status_code}")
            print(f"📄 Response Body: {response.text}")
            
            if response.status_code == 401:
                print("\n🚨 401 UNAUTHORIZED ANALYSIS:")
                
                # Parse error message
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', '')
                    
                    if 'scope' in error_message.lower():
                        print("❌ SCOPE ISSUE: Token lacks required permissions")
                        print("💡 SOLUTION: User must re-authorize with updated scopes")
                        print("🔗 Send user to your updated authorization URL")
                    elif 'token' in error_message.lower():
                        print("❌ TOKEN ISSUE: Token may be expired or invalid")
                        print("💡 SOLUTION: Refresh the access token")
                    else:
                        print(f"❌ OTHER AUTH ISSUE: {error_message}")
                        
                except:
                    print("❌ Could not parse error response")
                    
                print(f"\n📋 TROUBLESHOOTING CHECKLIST:")
                print(f"1. ✅ Check OAuth app has write:confluence-content scope")
                print(f"2. ✅ User re-authorized after scope was added") 
                print(f"3. ✅ Token is not expired")
                print(f"4. ✅ User has write permission to space")
                
            response.raise_for_status()
            return response.json()
            


    @staticmethod
    async def create_confluence_page(user_id: int, db: Session, cloud_id: str, space_id: str, title: str, content: str):
        """Create a new Confluence page using v1 API"""
        access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
        
        url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/rest/api/content"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "type": "page",
            "title": title,
            "space": {"key": space_id},
            "body": {
                "storage": {
                    "value": content,
                    "representation": "storage"
                }
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            
            if response.status_code != 200:
                print(f"❌ Create failed: {response.status_code}")
                print(f"Response: {response.text}")
                response.raise_for_status()
                
            return response.json()

    # @staticmethod
    # async def create_confluence_page_with_debug(user_id: int, db: Session, cloud_id: str, space_id: str, title: str, content: str):
    #     """Create page with detailed debugging"""
    #     try:
    #         access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
            
    #         # Debug token scopes first
    #         await ConfluenceOAuthService.debug_current_token_scopes(access_token)
            
    #         # Try to create the page
    #         return await ConfluenceOAuthService.create_confluence_page(
    #             user_id, db, cloud_id, space_id, title, content
    #         )
            
    #     except httpx.HTTPStatusError as e:
    #         print(f"❌ HTTP Error: {e.response.status_code}")
    #         print(f"Response body: {e.response.text}")
            
    #         if e.response.status_code == 403:
    #             raise HTTPException(
    #                 status_code=403, 
    #                 detail="Insufficient permissions. User may need to re-authorize the app with proper scopes."
    #             )
    #         elif e.response.status_code == 400:
    #             error_detail = e.response.json() if e.response.headers.get('content-type') == 'application/json' else e.response.text
    #             raise HTTPException(
    #                 status_code=400,
    #                 detail=f"Bad request: {error_detail}"
    #             )
    #         else:
    #             raise HTTPException(
    #                 status_code=e.response.status_code,
    #                 detail=f"Confluence API error: {e.response.text}"
    #             )

    @staticmethod
    async def update_confluence_page(user_id: int, db: Session, cloud_id: str, page_id: str, title: str, content: str, version: int):
        """Update an existing Confluence page"""
        access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
        
        url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/rest/api/content/{page_id}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "id": page_id,
            "type": "page",
            "title": title,
            "body": {
                "storage": {
                    "value": content,
                    "representation": "storage"
                }
            },
            "version": {"number": version + 1}
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.put(url, headers=headers, json=payload)
            
            if response.status_code != 200:
                print(f"❌ Update failed: {response.status_code}")
                print(f"Response: {response.text}")
                response.raise_for_status()
                
            return response.json()

    @staticmethod
    async def debug_current_token_scopes(access_token: str):
        """Debug what scopes the current access token actually has"""
        
        print("=== DEBUGGING CURRENT TOKEN SCOPES ===")
        
        # Get accessible resources with scopes
        url = "https://api.atlassian.com/oauth/token/accessible-resources"
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
            
            if response.status_code == 200:
                resources = response.json()
                
                for resource in resources:
                    print(f"\n📍 Resource: {resource.get('name')}")
                    print(f"   ID: {resource.get('id')}")
                    print(f"   URL: {resource.get('url')}")
                    
                    current_scopes = resource.get('scopes', [])
                    print(f"   Current Scopes: {current_scopes}")
                    
                    # Check required scopes for content operations
                    required_scopes = [
                        "read:confluence-content.all",
                        "write:confluence-content",
                        "read:confluence-space.summary", 
                        "read:confluence-user",
                        "read:content.metadata:confluence"  # Added this scope from your doc
                    ]
                    
                    print(f"\n   🔍 SCOPE CHECK:")
                    for scope in required_scopes:
                        if scope in current_scopes:
                            print(f"   ✅ {scope}")
                        else:
                            print(f"   ❌ {scope} - MISSING!")
                    
                    # Identify the problem
                    missing_scopes = [s for s in required_scopes if s not in current_scopes]
                    if missing_scopes:
                        print(f"\n   🚨 PROBLEM: Missing scopes: {missing_scopes}")
                        print(f"   💡 SOLUTION: User needs to re-authorize your app")
                        print(f"   🔗 Required OAuth scopes for your app configuration:")
                        for scope in required_scopes:
                            print(f"      - {scope}")
                    else:
                        print(f"\n   ✅ All required scopes present")
                        
            else:
                print(f"❌ Failed to get accessible resources: {response.status_code}")
                print(f"Response: {response.text}")

    @staticmethod
    async def convert_content_ids_to_types(user_id: int, db: Session, cloud_id: str, content_ids: list):
        """Convert content IDs to their content types (from your Atlassian doc)"""
        access_token = await ConfluenceOAuthService.get_valid_access_token(user_id, db)
        
        url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/content/convert-ids-to-types"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        payload = {
            "contentIds": content_ids
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
