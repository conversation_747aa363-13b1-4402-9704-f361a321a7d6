"""changeinpayment

Revision ID: daaee9bed004
Revises: 8c2673657722
Create Date: 2025-04-29 15:37:34.495757

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'daaee9bed004'
down_revision: Union[str, None] = '8c2673657722'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'payment_intent_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payments', 'payment_intent_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###
