from fastapi import APIRouter, Depends, Query
from app.database.session import get_db
from app.database.dependencies import get_current_user,role_required,optional_get_current_user
from sqlalchemy.orm import Session
from app.services.prd_chat_history_service import PRDChatHistoryService
from typing import Optional
from fastapi import HTTPException
from app.database.models.user_model import User
from app.database.enum import RoleEnum

router = APIRouter()

@router.get("/get_chat_history")
def get_chat_history(user_id: Optional[int] = Query(None, description="User ID"),current_user: Optional[User] = Depends(optional_get_current_user),db: Session = Depends(get_db),page: int = Query(1, description="Page number (starts from 1)"),
    page_size: int = Query(10, description="Number of records per page")):
    if user_id is not None:
        if not current_user or current_user.role != RoleEnum.Admin:
            raise HTTPException(status_code=403, detail="Not authorized to view other users' chat history.")
    else:
        user_id = current_user.id
    return PRDChatHistoryService.get_chat_history(user_id, db, page, page_size)

@router.get("/get_guest_user_chat_history")
def get_guest_user_chat_history(db: Session = Depends(get_db),current_user=Depends(role_required(["Admin"])),page: int = Query(1, description="Page number (starts from 1)"),
    page_size: int = Query(10, description="Number of records per page")):
    return PRDChatHistoryService.get_guest_user_chat_history(db, page, page_size)

@router.get("/get_chat_data_by_chat_id")
def get_chat_history_by_chat_id(current_user=Depends(get_current_user),db: Session = Depends(get_db),session_id: str = Query(..., description="Chat session ID")):
    return PRDChatHistoryService.get_chat_data_by_chat_id(current_user, db, session_id) 
