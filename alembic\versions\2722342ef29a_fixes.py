"""fixes

Revision ID: 2722342ef29a
Revises: e4faf863b95c
Create Date: 2025-07-02 15:21:14.591476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2722342ef29a'
down_revision: Union[str, None] = 'e4faf863b95c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('atlassian_tokens', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'access_token',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'refresh_token',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'cloud_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('atlassian_tokens', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_constraint('atlassian_tokens_user_id_fkey', 'atlassian_tokens', type_='foreignkey')
    op.create_foreign_key(None, 'atlassian_tokens', 'users', ['user_id'], ['id'])
    op.drop_constraint('billing_details_user_id_fkey', 'billing_details', type_='foreignkey')
    op.create_foreign_key(None, 'billing_details', 'users', ['user_id'], ['id'])
    op.drop_constraint('chat_history_session_id_fkey', 'chat_history', type_='foreignkey')
    op.create_foreign_key(None, 'chat_history', 'chat_sessions', ['session_id'], ['session_id'])
    op.drop_constraint('chat_sessions_user_id_fkey', 'chat_sessions', type_='foreignkey')
    op.create_foreign_key(None, 'chat_sessions', 'users', ['user_id'], ['id'])
    op.drop_constraint('notion_tokens_user_id_fkey', 'notion_tokens', type_='foreignkey')
    op.create_foreign_key(None, 'notion_tokens', 'users', ['user_id'], ['id'])
    op.drop_constraint('payments_user_id_fkey', 'payments', type_='foreignkey')
    op.create_foreign_key(None, 'payments', 'users', ['user_id'], ['id'])
    op.drop_constraint('subscriptions_user_id_fkey', 'subscriptions', type_='foreignkey')
    op.create_foreign_key(None, 'subscriptions', 'users', ['user_id'], ['id'])
    op.drop_constraint('user_sessions_user_id_fkey', 'user_sessions', type_='foreignkey')
    op.create_foreign_key(None, 'user_sessions', 'users', ['user_id'], ['id'])
    op.drop_constraint('user_uploads_user_id_fkey', 'user_uploads', type_='foreignkey')
    op.create_foreign_key(None, 'user_uploads', 'users', ['user_id'], ['id'])
    op.drop_column('users', 'is_deleted')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'user_uploads', type_='foreignkey')
    op.create_foreign_key('user_uploads_user_id_fkey', 'user_uploads', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'user_sessions', type_='foreignkey')
    op.create_foreign_key('user_sessions_user_id_fkey', 'user_sessions', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'subscriptions', type_='foreignkey')
    op.create_foreign_key('subscriptions_user_id_fkey', 'subscriptions', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'payments', type_='foreignkey')
    op.create_foreign_key('payments_user_id_fkey', 'payments', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'notion_tokens', type_='foreignkey')
    op.create_foreign_key('notion_tokens_user_id_fkey', 'notion_tokens', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'chat_sessions', type_='foreignkey')
    op.create_foreign_key('chat_sessions_user_id_fkey', 'chat_sessions', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'chat_history', type_='foreignkey')
    op.create_foreign_key('chat_history_session_id_fkey', 'chat_history', 'chat_sessions', ['session_id'], ['session_id'], ondelete='CASCADE')
    op.drop_constraint(None, 'billing_details', type_='foreignkey')
    op.create_foreign_key('billing_details_user_id_fkey', 'billing_details', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'atlassian_tokens', type_='foreignkey')
    op.create_foreign_key('atlassian_tokens_user_id_fkey', 'atlassian_tokens', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.alter_column('atlassian_tokens', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'cloud_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'refresh_token',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'access_token',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('atlassian_tokens', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###
