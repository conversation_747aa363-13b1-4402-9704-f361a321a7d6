from fastapi import API<PERSON><PERSON><PERSON>, Depends, Request,HTTPException
from app.database.session import get_db
from sqlalchemy.orm import Session
from app.services.notion_oauth_service import NotionOauthService
from app.database.dependencies import get_current_user
from app.schemas.notion_schema import TextToNotionRequest
from app.database.models.notion_workspace_model import NotionToken



router = APIRouter()


@router.post("/notion_oauth")
async def notion_oauth(request: Request,current_user=Depends(get_current_user), db: Session = Depends(get_db)):
    return await NotionOauthService.notion_oauth_service(request,current_user, db)


@router.get("/notion_callback")
async def notion_callback(request: Request, db: Session = Depends(get_db)):
    return await NotionOauthService.notion_callback_service(request, db)



@router.post("/send_prd_to_notion/")
async def send_prd_to_notion(request: TextToNotionRequest,current_user=Depends(get_current_user), db: Session = Depends(get_db)):
    notion_integration = db.query(NotionToken).filter_by(user_id=current_user.id).first()

    if not notion_integration:
        raise HTTPException(status_code=404, detail="Notion integration not found for user")

    if not notion_integration.access_token:
        raise HTTPException(status_code=400, detail="Missing Notion access token")

    if not notion_integration.duplicated_template_id:
        raise HTTPException(status_code=400, detail="Missing Notion parent page ID (duplicated_template_id)")

    notion_response = NotionOauthService.send_text_to_notion(
        access_token=notion_integration.access_token,
        parent_page_id=notion_integration.duplicated_template_id,
        title=request.title,
        text=request.text
    )

    return {"status": "success", "notion_response": notion_response}
