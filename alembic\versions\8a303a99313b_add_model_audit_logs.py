"""Add model audit logs

Revision ID: 8a303a99313b
Revises: 7fed474c28dc
Create Date: 2025-06-23 11:28:34.864603

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8a303a99313b'
down_revision: Union[str, None] = '7fed474c28dc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('admin_id', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_audit_logs_id'), 'audit_logs', ['id'], unique=False)
    op.drop_index('ix_free_uploads_free_upload_id', table_name='free_uploads')
    op.drop_table('free_uploads')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('free_uploads',
    sa.Column('free_upload_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('uploads', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('free_upload_id', name='free_uploads_pkey')
    )
    op.create_index('ix_free_uploads_free_upload_id', 'free_uploads', ['free_upload_id'], unique=False)
    op.drop_index(op.f('ix_audit_logs_id'), table_name='audit_logs')
    op.drop_table('audit_logs')
    # ### end Alembic commands ###
