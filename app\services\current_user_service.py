import os
import uuid
from fastapi import HTT<PERSON>Exception,status
from sqlalchemy.orm import Session
from app.database.models.user_model import User
from app.database.models.user_uploads_model import UserUpload
from typing import Optional
from datetime import date
from sqlalchemy import asc, desc
from app.schemas.user_schema import UpdateResponse,UpdateStatus
from datetime import datetime
from fastapi import UploadFile
from app.database.models.subscriptions_model import Subscription
from app.database.models.plan_model import Plan
from app.database.models.payment_model import Payment
from app.database.models.billing_details import BillingDetails
from app.database.models.deletion_request_model import DeletionRequest
from app.database.models.atlassian_tokens_model import AtlassianToken
from app.database.models.chat_session_model import ChatSession
from app.database.models.chat_history_model import ChatHistory
from app.database.models.notion_workspace_model import NotionToken
from app.database.models.user_session_model import UserSession

class CurrentUserService:
    @staticmethod
    def get_user_service(current_user, db: Session):
        if not current_user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")       
        
        subscription = db.query(Subscription).filter(Subscription.user_id == current_user.id).first()        
        if subscription:
            plan = db.query(Plan).filter(Plan.id == subscription.plan_id).first()
            current_user.plan_name = plan.plan_name if plan else None
        else:           
            free_plan = db.query(Plan).filter(Plan.price == 0).first()
            current_user.plan_name = free_plan.plan_name if free_plan else None
            
        return current_user
    
    @staticmethod
    def get_all_users_service(
            db: Session,
            page: int,
            page_size: int,
            search: Optional[str],
            is_active: Optional[bool],
            start_date: Optional[date],
            end_date: Optional[date],
            sort_by: str,
            sort_order: str
        ):
        query = db.query(User)
        if search:
            search_term = f"%{search}%"
            query = query.filter((User.name.ilike(search_term)) | (User.email.ilike(search_term)))
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        if start_date:
            query = query.filter(User.created_at >= start_date)
        if end_date:
            query = query.filter(User.created_at <= end_date)
        if sort_by in ["id", "name", "email", "created_at", "is_active"]:
            sort_column = getattr(User, sort_by)
            query = query.order_by(asc(sort_column) if sort_order == "asc" else desc(sort_column))
        else:
            query = query.order_by(desc(User.name))
        total = query.count()
        total_pages = (total + page_size - 1) // page_size
        users = query.offset((page - 1) * page_size).limit(page_size).all()
        return {
            "total": total,
            "total_pages": total_pages,
            "items": users
        }
    
    @staticmethod
    def update_user_service(request:UpdateResponse,db):
        user =db.query(User).filter(User.id == request.id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")
        existing_user =db.query(User).filter(request.email == User.email,request.id != User.id).first()
        if not request.email:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Email is required")
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already in use!")
        user.name =request.name
        user.email =request.email
        user.updated_at = datetime.now()
        db.commit()
        db.refresh(user)
        return {"message":"User updated successfully!"}
    
    @staticmethod
    def update_user_status_service(request:UpdateStatus,db):
        user =db.query(User).filter(User.id == request.id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User not found")
        user.is_active =request.is_active
        user.updated_at = datetime.now()
        db.commit()
        db.refresh(user)
        return {"message":"User status updated successfully!"}

    @staticmethod
    def delete_user_service(user_id: int, db: Session):
        deletion_order = [ChatHistory,ChatSession,UserUpload,
            Subscription,Payment,BillingDetails,DeletionRequest,
            AtlassianToken,NotionToken,UserSession
        ]
        for table in deletion_order:
            if hasattr(table, 'user_id'):
                db.query(table).filter(table.user_id == user_id).delete(synchronize_session=False)
            elif table == ChatHistory:
                session_ids = db.query(ChatSession.session_id).filter(ChatSession.user_id == user_id).all()
                session_ids = [s[0] for s in session_ids]
                db.query(ChatHistory).filter(ChatHistory.session_id.in_(session_ids)).delete(synchronize_session=False)
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="User not found"
            )
        db.delete(user)
        db.commit()
        return {"message": "User deleted successfully!"}    

    @staticmethod
    def upload_profile_picture_service(file: UploadFile, current_user, db: Session):
        upload_folder = "static/profile_image_upload"
        os.makedirs(upload_folder, exist_ok=True)
        file_ext = file.filename.split('.')[-1].lower()
        if file_ext not in ['jpg', 'png']:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Please upload a valid image (jpg/png)!")
        if current_user.profile_picture:
            old_file_path = current_user.profile_picture
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
        filename = f"{uuid.uuid4().hex}_{int(datetime.now().timestamp())}.{file_ext}"
        file_path = os.path.join(upload_folder, filename)

        with open(file_path, "wb") as buffer:
            buffer.write(file.file.read())
        current_user.profile_picture = file_path
        db.commit()
        db.refresh(current_user)
        return {
            "message": "Profile picture uploaded successfully",
            "profile_picture_url": f"{file_path}"
        }
    
    @staticmethod
    def get_user_uploads_service(current_user,db:Session):
        user_uploads = db.query(UserUpload).filter(UserUpload.user_id == current_user.id).first()
        if not user_uploads:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User uploads not found")
        return {
            "remaining_uploads": user_uploads.remaining_uploads
        }
    

    @staticmethod
    def get_user_data_service(user_id: int, db: Session, page: int, page_size: int, sort_by: str, sort_order: str, search: Optional[str]):

        existing_user = db.query(User).filter(User.id == user_id).first()
        if not existing_user:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="User Not Found !!")        
       
        payments_query = db.query(
            Payment, 
            Plan.plan_name.label("plan_name")
        ).join(
            Plan, Plan.id == Payment.plan_id
        ).filter(
            Payment.user_id == user_id
        )

        # Searching by payment_id or method
        if search:
            search_term = f"%{search}%"
            payments_query = payments_query.filter(
                (Payment.payment_id.ilike(search_term)) |
                (Payment.payment_method.ilike(search_term))
            )

        # Sorting
        sort_column = getattr(Payment, sort_by, Payment.created_at)
        if sort_order.lower() == "desc":
            sort_column = desc(sort_column)
        else:
            sort_column = asc(sort_column)

        payments_query = payments_query.order_by(sort_column)

        # Pagination
        total_payments = payments_query.count()
        payments_paginated = payments_query.offset((page - 1) * page_size).limit(page_size).all()

        payments = [
            {
                "payment_id": payment.payment_id,
                "payment_intent_id": payment.payment_intent_id,
                "payment_method": payment.payment_method,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_status": payment.payment_status,
                "created_at": payment.created_at,
                "plan_name": plan_name
            }
            for payment, plan_name in payments_paginated
        ]        
        subscriptions_query = db.query(
            Subscription,
            Plan.plan_name.label("plan_name")
        ).join(
            Plan, Plan.id == Subscription.plan_id
        ).filter(
            Subscription.user_id == user_id
        ).all()
        
        if not subscriptions_query:
            subscriptions = []
        else:
            subscriptions = []
            for subscription, plan_name in subscriptions_query:
                subscription_dict = {
                    "subscription_id": subscription.subscription_id,
                    "plan_name": plan_name,
                    "expiry_date": subscription.expiry_date,
                    "is_active": subscription.expiry_date > datetime.now(),
                    "created_at": subscription.created_at
                }
                subscriptions.append(subscription_dict)
        
        user_data = {
            "user_name": existing_user.name,
            "email": existing_user.email,
            "created_at": existing_user.created_at,
            "last_login": existing_user.last_login,
            "platform": existing_user.platform,
            "is_confirmed": existing_user.is_confirmed,
            "is_active": existing_user.is_active            
        }
        
        return {
            "user": user_data,
            "payments": {
                "data": payments,
                "page": page,
                "page_size": page_size,
                "total": total_payments
            },
            "subscriptions": subscriptions
        }

    
    @staticmethod
    def get_user_billing_details_service(current_user,db:Session):
        billing_details = db.query(BillingDetails).filter(BillingDetails.user_id == current_user.id).first()
        if not billing_details:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Billing details not found")
        return billing_details

    @staticmethod
    def delete_user_billing_details_service(current_user,db:Session):
        billing_details = db.query(BillingDetails).filter(BillingDetails.user_id == current_user.id).first()
        if not billing_details:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Billing details not found")
        db.delete(billing_details)
        db.commit()
        return {"message":"Billing details deleted successfully!"}
    
    @staticmethod
    def delete_request_data_service(reson:str,current_user,db:Session):
        user_reason = DeletionRequest(user_id=current_user.id,reason=reson,created_at=datetime.now(),updated_at=datetime.now()) 
        db.add(user_reason)
        db.commit() 
        user_data = db.query(User).filter(User.id == current_user.id).first()
        user_data.is_deleted = True
        db.commit()
        db.refresh(user_data)       
        return {"message":"Your Request has been submitted successfully!"}
    

    @staticmethod
    def get_deletion_request_service(user_id,db:Session):
        user_reason = db.query(DeletionRequest).filter(DeletionRequest.user_id == user_id).first()
        if not user_reason:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Deletion request not found")
        return user_reason
